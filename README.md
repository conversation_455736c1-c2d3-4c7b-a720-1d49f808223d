# MADDPG Multi-Task Federated Learning System

This project implements a Multi-Agent Deep Deterministic Policy Gradient (MADDPG) system for scheduling multiple federated learning (FL) tasks across distributed clients. The system uses an event-driven simulation where `env.step()` is triggered only by meaningful events: task arrivals and round completions.

## System Overview

The system consists of:
- **Multiple Clients**: Each running a MADDPG agent for intelligent task scheduling
- **Central Server**: Handles model aggregation and coordinates FL training
- **Event-Driven Environment**: Triggers `env.step()` only on task arrivals and round completions
- **Resource Management**: Each client manages computation (τ), bandwidth (q), and power (P) resources

## Key Features

### Core Mechanism
- **Event-Driven Simulation**: `env.step()` called only when:
  1. A new FL task arrives at a client
  2. A FL task completes a global round on a client
- **5-Parameter Actions**: Each agent outputs:
  - `Selected_ij`: Binary decision to execute task j
  - `q_ij`: Computation resource ratio (0-1)
  - `B_ij`: Bandwidth allocation (Mbps)
  - `P_ij`: Power consumption (Watts)
  - `start_time_ij`: Execution start time

### Advanced Features
- **Dynamic Task Arrival**: FL tasks arrive following Poisson process
- **Resource Constraints**: Computation, bandwidth, and power limitations
- **Multi-Objective Optimization**: Balance accuracy, energy, and deadlines
- **Federated Learning**: Real FL rounds with model aggregation
- **MADDPG Training**: Multi-agent reinforcement learning

## Quick Start

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd maddpg-multi-task

# Install dependencies
pip install -r requirements.txt
```

### Run Demo
```bash
# Quick demonstration
python demo.py
```

### Run Training
```bash
# Train MADDPG agents
python main.py --config config/default.yaml

# Train with custom settings
python main.py --config config/default.yaml --experiment-name my_experiment --log-dir logs

# Resume training
python main.py --resume models/episode_100_agent_0.pth

# Evaluation only
python main.py --eval-only --resume models/best_model_agent_0.pth
```

### Run Tests
```bash
# Basic functionality tests
python -m pytest tests/test_basic_functionality.py -v

# Quick system test
python experiments/quick_test.py
```

## Configuration

The system is configured via YAML files. Key parameters:

```yaml
# Environment Settings
environment:
  max_simulation_time: 10000
  time_step: 1.0

# Client Settings
clients:
  num_clients: 5
  total_computation_capacity: 100.0
  total_bandwidth_capacity: 50.0
  max_power_consumption: 10.0

# Task Settings
tasks:
  arrival_rate: 0.1
  min_deadline: 100
  max_deadline: 500
  max_global_rounds: 20

# MADDPG Settings
maddpg:
  actor_lr: 0.001
  critic_lr: 0.001
  gamma: 0.95
  tau: 0.01
  buffer_size: 100000
  batch_size: 64

# Reward Settings
rewards:
  accuracy_weight: 1.0
  energy_weight: 0.5
  deadline_weight: 2.0
  completion_bonus: 10.0
```

## Project Structure

```
├── src/
│   ├── environment/     # Event-driven simulation environment
│   │   ├── maddpg_env.py       # Main environment class
│   │   ├── state_manager.py    # State representation
│   │   └── reward_calculator.py # Reward computation
│   ├── agents/         # MADDPG agents
│   │   ├── maddpg_agent.py     # MADDPG agent implementation
│   │   ├── networks.py         # Actor-Critic networks
│   │   └── replay_buffer.py    # Experience replay
│   ├── clients/        # Client implementations
│   │   ├── client.py           # FL client with resource management
│   │   └── resource_manager.py # Resource allocation and scheduling
│   ├── server/         # Central server
│   │   └── fl_server.py        # Model aggregation server
│   ├── tasks/          # FL task management
│   │   ├── fl_task.py          # FL task representation
│   │   └── task_manager.py     # Task lifecycle management
│   └── utils/          # Utilities and helpers
│       ├── config_loader.py    # Configuration management
│       ├── logger.py           # Training logging
│       └── visualization.py    # Performance visualization
├── config/             # Configuration files
│   └── default.yaml    # Default configuration
├── tests/              # Unit tests
│   └── test_basic_functionality.py
├── experiments/        # Experiment scripts
│   └── quick_test.py   # Quick system validation
├── main.py            # Main training script
├── demo.py            # Demonstration script
└── requirements.txt   # Python dependencies
```

## System Architecture

The system implements the following workflow:

### 1. Initialization
- All clients initialize with total computation capacity `τᵢᵗᵒᵗᵃˡ`, bandwidth `qᵢᵗᵒᵗᵃˡ`, and power limits
- Each client maintains empty task queue and schedule queue
- Central server and environment simulator start

### 2. Event-Driven Execution
The simulation progresses through events:

#### Task Arrival Event
1. New FL task j arrives at client i at time t
2. Triggers `env.step()`
3. Environment provides state `sᵢ` to agent Aᵢ
4. Agent outputs action: `(Selected_ij, q_ij, B_ij, P_ij, start_time_ij)`
5. If `Selected_ij = True`:
   - Calculate execution time: `G_ij = T_cmp_ij(q_ij) + T_com_ij(B_ij)`
   - Add to schedule: `[start_time_ij, start_time_ij + G_ij, j, Round]`
   - Lock resources `(q_ij, B_ij, P_ij)` for duration `G_ij`

#### Round Completion Event
1. Task j completes round R at client i at time `start_time_ij + G_ij`
2. Triggers `env.step()`
3. Server aggregates models and updates global accuracy
4. Environment calculates reward based on accuracy gain, energy, deadlines
5. Agent decides on next round with new action parameters

### 3. Resource Management
- **Computation**: `q_ij` ∈ [0,1] represents fraction of total capacity
- **Bandwidth**: `B_ij` in Mbps for model upload time `T_com_ij`
- **Power**: `P_ij` in Watts for energy consumption `E_ij = P_ij × G_ij`
- **Scheduling**: Non-overlapping resource allocation in time

### 4. Multi-Objective Rewards
- **Accuracy**: Reward for FL convergence progress
- **Energy**: Penalty for high power consumption
- **Deadlines**: Penalty for missed deadlines, bonus for early completion
- **Global**: System-wide performance bonuses

## Key Algorithms

### MADDPG Agent Decision Process
```python
def get_action(state, task_context):
    # Neural network forward pass
    action_probs = actor_network(state)

    # 5-parameter action
    action = {
        'selected': sample_bernoulli(action_probs.selection),
        'computation_ratio': action_probs.computation,  # q_ij
        'bandwidth': action_probs.bandwidth,            # B_ij
        'power': action_probs.power,                    # P_ij
        'start_time': current_time + action_probs.timing_offset
    }

    return action
```

### Resource Allocation
```python
def schedule_task_round(task_id, action):
    # Calculate execution time
    T_cmp = data_size * local_rounds / (total_computation * q_ij)
    T_com = model_size_MB * 8 / B_ij  # MB to Mbits / Mbps
    G_ij = T_cmp + T_com

    # Check resource availability
    if resources_available(q_ij, B_ij, P_ij, start_time, G_ij):
        schedule_entry = ScheduleEntry(start_time, start_time + G_ij,
                                     task_id, round_num, q_ij, B_ij, P_ij)
        add_to_schedule(schedule_entry)
        return True
    return False
```

### Reward Calculation
```python
def calculate_reward(client, task, execution_result, current_time):
    # Accuracy improvement reward
    accuracy_reward = accuracy_weight * accuracy_improvement * 100

    # Energy efficiency penalty
    energy_penalty = energy_weight * (energy_consumed / max_energy)

    # Deadline urgency
    urgency_reward = deadline_weight * task.get_urgency_score(current_time)

    # Completion bonus
    completion_bonus = completion_bonus if task.is_completed else 0

    return accuracy_reward - energy_penalty + urgency_reward + completion_bonus
```

## Performance Metrics

The system tracks multiple performance indicators:

- **Task Completion Rate**: Fraction of tasks reaching target accuracy
- **Energy Efficiency**: Tasks completed per unit energy consumed
- **Deadline Adherence**: Fraction of tasks completed before deadline
- **Resource Utilization**: Average usage of computation, bandwidth, power
- **Convergence Speed**: Rounds needed to reach target accuracy
- **System Throughput**: Tasks processed per time unit

## Experimental Results

The system demonstrates:
- **Adaptive Scheduling**: Agents learn to prioritize urgent tasks
- **Resource Efficiency**: Balanced utilization across computation, bandwidth, power
- **Multi-Task Coordination**: Effective handling of concurrent FL tasks
- **Scalability**: Performance maintained with increasing clients and tasks

## Citation

If you use this code in your research, please cite:

```bibtex
@article{maddpg_fl_2024,
  title={Multi-Agent Deep Deterministic Policy Gradient for Multi-Task Federated Learning Scheduling},
  author={Your Name},
  journal={Conference/Journal Name},
  year={2024}
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Contact

For questions or issues, please open an issue on GitHub or contact [<EMAIL>].
