# MADDPG Multi-Task Federated Learning System

This project implements a Multi-Agent Deep Deterministic Policy Gradient (MADDPG) system for scheduling multiple federated learning (FL) tasks across distributed clients.

## System Overview

The system consists of:
- **Multiple Clients**: Each running a MADDPG agent for task scheduling
- **Central Server**: Handles model aggregation and task distribution
- **Event-Driven Environment**: Triggers env.step() on task arrivals and round completions

## Key Features

- **Dynamic Task Arrival**: FL tasks arrive dynamically and are assigned to clients
- **Resource Management**: Each client manages computation (τ), communication bandwidth (q), and power (P)
- **5-Parameter Actions**: Agents output (Selected_ij, q_ij, B_ij, P_ij, start_time_ij) for each task round
- **Event-Driven Simulation**: Environment steps only on meaningful events (task arrival/completion)

## Installation

```bash
pip install -r requirements.txt
```

## Usage

```bash
python main.py --config config/default.yaml
```

## Project Structure

```
├── src/
│   ├── environment/     # Simulation environment
│   ├── agents/         # MADDPG agents
│   ├── clients/        # Client implementations
│   ├── server/         # Central server
│   ├── tasks/          # FL task management
│   └── utils/          # Utilities and helpers
├── config/             # Configuration files
├── tests/              # Unit tests
└── experiments/        # Experiment scripts
```
