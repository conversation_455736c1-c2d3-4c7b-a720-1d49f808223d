# MADDPG Multi-Task FL Configuration

# Environment Settings
environment:
  max_simulation_time: 10000  # Maximum simulation time
  time_step: 1.0             # Time step for simulation
  
# Client Settings
clients:
  num_clients: 5             # Number of clients in the system
  total_computation_capacity: 100.0  # τ_i^total for each client
  total_bandwidth_capacity: 50.0     # q_i^total for each client (Mbps)
  max_power_consumption: 10.0        # Maximum power consumption per client

# Task Settings
tasks:
  arrival_rate: 0.1          # Average task arrival rate (tasks per time unit)
  min_deadline: 100          # Minimum task deadline
  max_deadline: 500          # Maximum task deadline
  min_target_accuracy: 0.8   # Minimum target accuracy
  max_target_accuracy: 0.95  # Maximum target accuracy
  min_data_size: 1000        # Minimum data size per client
  max_data_size: 10000       # Maximum data size per client
  max_global_rounds: 20      # Maximum global rounds per task

# MADDPG Settings
maddpg:
  actor_lr: 0.001           # Actor learning rate
  critic_lr: 0.001          # Critic learning rate
  gamma: 0.95               # Discount factor
  tau: 0.01                 # Soft update parameter
  buffer_size: 100000       # Replay buffer size
  batch_size: 64            # Batch size for training
  hidden_dim: 128           # Hidden layer dimension
  
# Training Settings
training:
  num_episodes: 1000        # Number of training episodes
  max_episode_length: 1000  # Maximum steps per episode
  save_interval: 100        # Save model every N episodes
  log_interval: 10          # Log progress every N episodes

# Reward Settings
rewards:
  accuracy_weight: 1.0      # Weight for accuracy improvement
  energy_weight: 0.5        # Weight for energy consumption
  deadline_weight: 2.0      # Weight for deadline violation
  completion_bonus: 10.0    # Bonus for task completion
