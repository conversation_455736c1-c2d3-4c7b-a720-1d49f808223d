"""
Demo script for MADDPG Multi-Task Federated Learning System
"""
import os
import time
import numpy as np

from src.environment.maddpg_env import MADDPGEnvironment
from src.utils.config_loader import load_config


def run_demo():
    """Run a demonstration of the MADDPG FL system"""
    print("=" * 60)
    print("MADDPG Multi-Task Federated Learning System Demo")
    print("=" * 60)
    
    # Load configuration
    config_path = "config/default.yaml"
    if not os.path.exists(config_path):
        print(f"Configuration file not found: {config_path}")
        print("Please run from the project root directory.")
        return
    
    try:
        config = load_config(config_path)
        print(f"✓ Configuration loaded from {config_path}")
    except Exception as e:
        print(f"✗ Failed to load configuration: {e}")
        return
    
    # Modify config for demo (smaller scale)
    config['clients']['num_clients'] = 3
    config['tasks']['arrival_rate'] = 0.3
    config['training']['max_episode_length'] = 30
    config['environment']['max_simulation_time'] = 200
    
    print(f"✓ Demo configuration: {config['clients']['num_clients']} clients, "
          f"arrival rate {config['tasks']['arrival_rate']}")
    
    # Initialize environment
    try:
        env = MADDPGEnvironment(config)
        print("✓ Environment initialized successfully")
    except Exception as e:
        print(f"✗ Failed to initialize environment: {e}")
        return
    
    print("\nSystem Components:")
    print(f"  - Clients: {len(env.clients)}")
    print(f"  - MADDPG Agents: {len(env.agents)}")
    print(f"  - State dimension: {env.state_manager.state_dim}")
    print(f"  - Action dimension: 5 (Selected, q_ij, B_ij, P_ij, start_time)")
    
    # Run demonstration episode
    print("\n" + "=" * 40)
    print("Running Demonstration Episode")
    print("=" * 40)
    
    # Reset environment
    states = env.reset()
    print(f"Episode started at time {env.current_time}")
    
    episode_done = False
    step_count = 0
    events_processed = []
    
    while not episode_done and step_count < 25:  # Limit for demo
        print(f"\n--- Step {step_count + 1} ---")
        
        # Take environment step
        next_states, rewards, dones, info = env.step()
        
        # Record event
        event_info = {
            'step': step_count + 1,
            'time': env.current_time,
            'event_type': info.get('event_type', 'none'),
            'task_id': info.get('task_id'),
            'client_id': info.get('client_id'),
            'active_tasks': info.get('active_tasks', 0),
            'rewards': rewards
        }
        events_processed.append(event_info)
        
        # Print event details
        print(f"Time: {env.current_time:.1f}")
        print(f"Event: {event_info['event_type']}")
        
        if event_info['event_type'] == 'arrival':
            print(f"  → Task {event_info['task_id']} arrived at Client {event_info['client_id']}")
            
            # Show agent decision
            if event_info['client_id'] is not None and event_info['task_id'] is not None:
                action = env.get_agent_action_for_task(event_info['client_id'], event_info['task_id'])
                print(f"  → Agent decision: Selected={action.get('selected', False)}")
                if action.get('selected', False):
                    print(f"    - Computation: {action.get('computation_ratio', 0):.2f}")
                    print(f"    - Bandwidth: {action.get('bandwidth', 0):.1f} Mbps")
                    print(f"    - Power: {action.get('power', 0):.1f} W")
                    print(f"    - Start time: {action.get('start_time', 0):.1f}")
        
        elif event_info['event_type'] == 'round_complete':
            print(f"  → Task {event_info['task_id']} round completed at Client {event_info['client_id']}")
        
        elif event_info['event_type'] == 'deadline':
            print(f"  → Task {event_info['task_id']} deadline expired")
        
        print(f"Active tasks: {event_info['active_tasks']}")
        print(f"Rewards: {[f'{r:.2f}' for r in rewards.values()]}")
        
        # Show client status
        for client_id, client in env.clients.items():
            resource_usage = client.resource_manager.get_current_resource_usage(env.current_time)
            print(f"  Client {client_id}: Tasks={len(client.task_queue)}, "
                  f"CPU={resource_usage['computation_ratio']:.2f}, "
                  f"BW={resource_usage['bandwidth_usage']:.1f}")
        
        # Update for next iteration
        states = next_states
        episode_done = all(dones.values())
        step_count += 1
        
        # Small delay for readability
        time.sleep(0.5)
    
    # Episode summary
    print("\n" + "=" * 40)
    print("Episode Summary")
    print("=" * 40)
    
    final_stats = env.get_episode_statistics()
    
    print(f"Episode duration: {step_count} steps, {env.current_time:.1f} time units")
    print(f"Events processed: {len(events_processed)}")
    print(f"Tasks completed: {final_stats.get('total_tasks_completed', 0)}")
    print(f"Tasks expired: {final_stats.get('total_tasks_expired', 0)}")
    print(f"Total energy consumed: {final_stats.get('total_energy_consumed', 0):.2f}")
    
    # Agent performance
    print("\nAgent Performance:")
    avg_rewards = final_stats.get('avg_rewards', {})
    total_rewards = final_stats.get('total_rewards', {})
    
    for client_id in env.client_ids:
        avg_reward = avg_rewards.get(client_id, 0)
        total_reward = total_rewards.get(client_id, 0)
        client_stats = final_stats.get('client_stats', {}).get(client_id, {})
        
        print(f"  Agent {client_id}:")
        print(f"    - Average reward: {avg_reward:.3f}")
        print(f"    - Total reward: {total_reward:.2f}")
        print(f"    - Rounds executed: {client_stats.get('total_rounds_executed', 0)}")
        print(f"    - Energy consumed: {client_stats.get('total_energy_consumed', 0):.2f}")
    
    # System metrics
    task_stats = final_stats.get('task_stats', {})
    completion_rate = task_stats.get('completion_rate', 0)
    expiry_rate = task_stats.get('expiry_rate', 0)
    
    print(f"\nSystem Metrics:")
    print(f"  - Task completion rate: {completion_rate:.3f}")
    print(f"  - Task expiry rate: {expiry_rate:.3f}")
    print(f"  - Energy efficiency: {final_stats.get('total_tasks_completed', 0) / max(1, final_stats.get('total_energy_consumed', 1)):.3f} tasks/energy")
    
    # Event timeline
    print(f"\nEvent Timeline:")
    for event in events_processed[:10]:  # Show first 10 events
        print(f"  {event['step']:2d}. t={event['time']:5.1f} - {event['event_type']:12s} "
              f"(Task {event['task_id']}, Client {event['client_id']})")
    
    if len(events_processed) > 10:
        print(f"  ... and {len(events_processed) - 10} more events")
    
    print("\n" + "=" * 60)
    print("Demo completed! The system demonstrates:")
    print("✓ Event-driven simulation with task arrivals and completions")
    print("✓ MADDPG agents making 5-parameter scheduling decisions")
    print("✓ Resource management and constraint handling")
    print("✓ Reward calculation based on performance metrics")
    print("✓ Multi-client federated learning coordination")
    print("=" * 60)


def show_system_architecture():
    """Show system architecture overview"""
    print("\n" + "=" * 60)
    print("System Architecture Overview")
    print("=" * 60)
    
    architecture = """
    ┌─────────────────────────────────────────────────────────────┐
    │                    MADDPG FL System                         │
    └─────────────────────────────────────────────────────────────┘
    
    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │   Client 0      │    │   Client 1      │    │   Client 2      │
    │                 │    │                 │    │                 │
    │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
    │ │ MADDPG      │ │    │ │ MADDPG      │ │    │ │ MADDPG      │ │
    │ │ Agent       │ │    │ │ Agent       │ │    │ │ Agent       │ │
    │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
    │                 │    │                 │    │                 │
    │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
    │ │ Task Queue  │ │    │ │ Task Queue  │ │    │ │ Task Queue  │ │
    │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
    │                 │    │                 │    │                 │
    │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
    │ │ Resource    │ │    │ │ Resource    │ │    │ │ Resource    │ │
    │ │ Manager     │ │    │ │ Manager     │ │    │ │ Manager     │ │
    │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
    └─────────────────┘    └─────────────────┘    └─────────────────┘
                │                    │                    │
                └────────────────────┼────────────────────┘
                                     │
                        ┌─────────────────────────┐
                        │     Central Server      │
                        │                         │
                        │ ┌─────────────────────┐ │
                        │ │ Model Aggregation   │ │
                        │ └─────────────────────┘ │
                        │                         │
                        │ ┌─────────────────────┐ │
                        │ │ Task Distribution   │ │
                        │ └─────────────────────┘ │
                        └─────────────────────────┘
                                     │
                        ┌─────────────────────────┐
                        │   Event-Driven Env     │
                        │                         │
                        │ • Task Arrivals         │
                        │ • Round Completions     │
                        │ • Deadline Expiry       │
                        │ • Reward Calculation    │
                        └─────────────────────────┘
    
    Key Features:
    • Each client runs a MADDPG agent for task scheduling
    • 5-parameter actions: (Selected_ij, q_ij, B_ij, P_ij, start_time_ij)
    • Event-driven simulation (env.step() on meaningful events only)
    • Resource constraints: computation, bandwidth, power
    • Federated learning with model aggregation
    • Multi-objective rewards: accuracy, energy, deadlines
    """
    
    print(architecture)


if __name__ == "__main__":
    show_system_architecture()
    run_demo()
