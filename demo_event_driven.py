"""
Event-Driven Demo for MADDPG Multi-Task Federated Learning System

This demo shows the correct event-driven mechanism where env.step() 
is called ONLY when meaningful events occur:
1. Task arrival at a client
2. FL round completion at a client
"""
import os
import time
import numpy as np

from src.environment.maddpg_env import MADDPGEnvironment
from src.utils.config_loader import load_config


def create_demo_config():
    """Create configuration for event-driven demo"""
    return {
        'environment': {
            'max_simulation_time': 300,  # Shorter simulation
            'time_step': 1.0
        },
        'clients': {
            'num_clients': 3,
            'total_computation_capacity': 100.0,
            'total_bandwidth_capacity': 50.0,
            'max_power_consumption': 10.0
        },
        'tasks': {
            'arrival_rate': 0.3,  # Higher arrival rate for demo
            'min_deadline': 50,
            'max_deadline': 150,
            'min_target_accuracy': 0.8,
            'max_target_accuracy': 0.95,
            'min_data_size': 500,
            'max_data_size': 2000,
            'max_global_rounds': 8
        },
        'maddpg': {
            'actor_lr': 0.001,
            'critic_lr': 0.001,
            'gamma': 0.95,
            'tau': 0.01,
            'buffer_size': 1000,
            'batch_size': 16,
            'hidden_dim': 32
        },
        'training': {
            'num_episodes': 1,  # Single episode for demo
            'max_episode_length': 50,
            'save_interval': 10,
            'log_interval': 1
        },
        'rewards': {
            'accuracy_weight': 1.0,
            'energy_weight': 0.5,
            'deadline_weight': 2.0,
            'completion_bonus': 10.0
        },
        'max_tasks_per_client': 5,
        'max_schedule_entries': 10
    }


def demonstrate_event_driven_mechanism():
    """Demonstrate the true event-driven mechanism"""
    print("=" * 80)
    print("MADDPG Multi-Task FL System - Event-Driven Mechanism Demo")
    print("=" * 80)
    
    print("\nKey Point: env.step() is called ONLY when events occur!")
    print("Events: 1) Task arrival  2) FL round completion  3) Deadline expiry")
    print("-" * 80)
    
    # Initialize environment
    config = create_demo_config()
    env = MADDPGEnvironment(config)
    
    print(f"\nSystem initialized:")
    print(f"  - Clients: {len(env.clients)}")
    print(f"  - Scheduled events: {len(env.task_manager.event_queue)}")
    print(f"  - Simulation time limit: {config['environment']['max_simulation_time']}")
    
    # Show initial event queue
    print(f"\nInitial Event Queue (first 10 events):")
    temp_events = []
    for i in range(min(10, len(env.task_manager.event_queue))):
        event = env.task_manager.event_queue[i]
        temp_events.append(event)
        print(f"  {i+1}. t={event.time:.1f} - {event.event_type} "
              f"(Task {event.task_id} → Client {event.client_id})")
    
    print(f"\n" + "=" * 80)
    print("Starting Event-Driven Simulation")
    print("=" * 80)
    
    # Run the event-driven episode
    episode_results = env.run_episode()
    
    print(f"\n" + "=" * 80)
    print("Event-Driven Simulation Completed")
    print("=" * 80)
    
    # Analyze the results
    events_processed = episode_results['events_processed']
    final_stats = episode_results['final_stats']
    
    print(f"\nSimulation Summary:")
    print(f"  - Total events processed: {len(events_processed)}")
    print(f"  - env.step() called exactly: {len(events_processed)} times")
    print(f"  - Final simulation time: {env.current_time:.1f}")
    print(f"  - Tasks completed: {final_stats.get('total_tasks_completed', 0)}")
    print(f"  - Tasks expired: {final_stats.get('total_tasks_expired', 0)}")
    print(f"  - Total energy consumed: {final_stats.get('total_energy_consumed', 0):.2f}")
    
    # Show event timeline
    print(f"\nEvent Timeline (env.step() calls):")
    print("-" * 60)
    
    event_counts = {'arrival': 0, 'round_complete': 0, 'deadline': 0}
    
    for i, event_info in enumerate(events_processed):
        event_type = event_info.get('event_type', 'unknown')
        task_id = event_info.get('task_id', 'N/A')
        client_id = event_info.get('client_id', 'N/A')
        current_time = event_info.get('current_time', 0)
        active_tasks = event_info.get('active_tasks', 0)
        
        event_counts[event_type] = event_counts.get(event_type, 0) + 1
        
        print(f"Step {i+1:2d}: t={current_time:6.1f} - {event_type:12s} "
              f"(Task {task_id:2s}, Client {client_id}) - Active: {active_tasks}")
        
        # Show agent decision for task arrivals
        if event_type == 'arrival' and client_id != 'N/A' and task_id != 'N/A':
            try:
                action = env.get_agent_action_for_task(int(client_id), int(task_id))
                if action.get('selected', False):
                    print(f"         → Agent Decision: ACCEPT "
                          f"(CPU: {action.get('computation_ratio', 0):.2f}, "
                          f"BW: {action.get('bandwidth', 0):.1f}, "
                          f"Power: {action.get('power', 0):.1f})")
                else:
                    print(f"         → Agent Decision: REJECT")
            except:
                print(f"         → Agent Decision: ERROR")
    
    print(f"\nEvent Type Summary:")
    for event_type, count in event_counts.items():
        print(f"  - {event_type}: {count} times")
    
    # Show client final states
    print(f"\nFinal Client States:")
    for client_id, client in env.clients.items():
        resource_usage = client.resource_manager.get_current_resource_usage(env.current_time)
        print(f"  Client {client_id}:")
        print(f"    - Active tasks: {len(client.task_queue)}")
        print(f"    - Completed tasks: {len(client.completed_tasks)}")
        print(f"    - Total energy: {client.total_energy_consumed:.2f}")
        print(f"    - Rounds executed: {client.total_rounds_executed}")
    
    # Demonstrate the key insight
    print(f"\n" + "=" * 80)
    print("KEY INSIGHT: Event-Driven vs Traditional RL")
    print("=" * 80)
    
    print(f"\n✓ Event-Driven (Our Implementation):")
    print(f"  - env.step() called: {len(events_processed)} times")
    print(f"  - Only when meaningful events occur")
    print(f"  - Simulation time: {env.current_time:.1f} units")
    print(f"  - Efficient: No wasted computation")
    
    traditional_steps = int(env.current_time / config['environment']['time_step'])
    print(f"\n✗ Traditional RL (Continuous):")
    print(f"  - Would call env.step(): {traditional_steps} times")
    print(f"  - Every time step regardless of events")
    print(f"  - Wasteful: {traditional_steps - len(events_processed)} unnecessary calls")
    
    efficiency_gain = (traditional_steps - len(events_processed)) / traditional_steps * 100
    print(f"\n📊 Efficiency Gain: {efficiency_gain:.1f}% fewer env.step() calls")
    
    return episode_results


def show_event_driven_architecture():
    """Show the event-driven architecture"""
    print("\n" + "=" * 80)
    print("Event-Driven Architecture")
    print("=" * 80)
    
    architecture = """
    Traditional RL Loop:                Event-Driven FL Loop:
    ┌─────────────────────┐            ┌─────────────────────┐
    │ for t in time_steps │            │ while events_exist  │
    │   env.step()        │     VS     │   event = get_next()│
    │   # every timestep  │            │   env.step()        │
    │ end                 │            │   # only on events  │
    └─────────────────────┘            └─────────────────────┘
    
    Event Queue (Priority Queue by Time):
    ┌─────────────────────────────────────────────────────────┐
    │ t=5.2  : Task 1 arrives at Client 0                    │
    │ t=12.7 : Task 2 arrives at Client 1                    │
    │ t=18.3 : Task 1 Round 1 completes at Client 0          │
    │ t=25.1 : Task 3 arrives at Client 2                    │
    │ t=31.8 : Task 2 Round 1 completes at Client 1          │
    │ t=45.2 : Task 1 Round 2 completes at Client 0          │
    │ ...                                                     │
    └─────────────────────────────────────────────────────────┘
    
    Event Processing:
    1. Task Arrival Event:
       - env.step() triggered
       - Agent gets state s_i
       - Agent outputs action (Selected_ij, q_ij, B_ij, P_ij, start_time_ij)
       - If Selected_ij = True: schedule round completion event
    
    2. Round Completion Event:
       - env.step() triggered  
       - Execute FL round with locked resources (q_ij, B_ij, P_ij)
       - Server aggregates models
       - Calculate rewards
       - Agent decides on next round
    
    3. Deadline Expiry Event:
       - env.step() triggered
       - Apply deadline violation penalties
       - Remove expired tasks
    """
    
    print(architecture)


if __name__ == "__main__":
    # Show architecture first
    show_event_driven_architecture()
    
    # Run the demonstration
    results = demonstrate_event_driven_mechanism()
    
    print(f"\n" + "=" * 80)
    print("Demo completed! This shows the correct event-driven mechanism")
    print("where env.step() is called ONLY when meaningful events occur.")
    print("=" * 80)
