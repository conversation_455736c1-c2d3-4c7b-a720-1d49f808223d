"""
Quick test script to verify the MADDPG FL system works
"""
import sys
import os
import numpy as np

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.environment.maddpg_env import MADDPGEnvironment
from src.utils.config_loader import load_config


def create_test_config():
    """Create a minimal test configuration"""
    return {
        'environment': {
            'max_simulation_time': 500,
            'time_step': 1.0
        },
        'clients': {
            'num_clients': 3,
            'total_computation_capacity': 100.0,
            'total_bandwidth_capacity': 50.0,
            'max_power_consumption': 10.0
        },
        'tasks': {
            'arrival_rate': 0.2,
            'min_deadline': 50,
            'max_deadline': 200,
            'min_target_accuracy': 0.8,
            'max_target_accuracy': 0.95,
            'min_data_size': 500,
            'max_data_size': 2000,
            'max_global_rounds': 10
        },
        'maddpg': {
            'actor_lr': 0.001,
            'critic_lr': 0.001,
            'gamma': 0.95,
            'tau': 0.01,
            'buffer_size': 1000,
            'batch_size': 16,
            'hidden_dim': 32
        },
        'training': {
            'num_episodes': 5,
            'max_episode_length': 50,
            'save_interval': 2,
            'log_interval': 1
        },
        'rewards': {
            'accuracy_weight': 1.0,
            'energy_weight': 0.5,
            'deadline_weight': 2.0,
            'completion_bonus': 10.0
        },
        'max_tasks_per_client': 5,
        'max_schedule_entries': 10
    }


def test_single_episode():
    """Test a single episode"""
    print("=== Testing Single Episode ===")
    
    config = create_test_config()
    env = MADDPGEnvironment(config)
    
    # Reset environment
    print("Resetting environment...")
    states = env.reset()
    print(f"Initial states shape: {[state.shape for state in states.values()]}")
    
    episode_done = False
    step_count = 0
    total_rewards = {client_id: 0.0 for client_id in env.client_ids}
    
    print("Running episode...")
    while not episode_done and step_count < 20:  # Limit steps for quick test
        # Take environment step
        next_states, rewards, dones, info = env.step()
        
        # Accumulate rewards
        for client_id, reward in rewards.items():
            total_rewards[client_id] += reward
        
        # Print step info
        print(f"Step {step_count}: Event={info.get('event_type', 'none')}, "
              f"Active tasks={info.get('active_tasks', 0)}, "
              f"Rewards={[f'{r:.2f}' for r in rewards.values()]}")
        
        # Update states
        states = next_states
        episode_done = all(dones.values())
        step_count += 1
    
    # Get final statistics
    final_stats = env.get_episode_statistics()
    
    print(f"\nEpisode completed in {step_count} steps")
    print(f"Total rewards: {[f'{r:.2f}' for r in total_rewards.values()]}")
    print(f"Tasks completed: {final_stats.get('total_tasks_completed', 0)}")
    print(f"Tasks expired: {final_stats.get('total_tasks_expired', 0)}")
    print(f"Total energy: {final_stats.get('total_energy_consumed', 0):.2f}")
    
    return final_stats


def test_agent_actions():
    """Test agent action generation"""
    print("\n=== Testing Agent Actions ===")
    
    config = create_test_config()
    env = MADDPGEnvironment(config)
    
    # Reset and get initial state
    states = env.reset()
    
    # Test action generation for each agent
    for client_id, agent in env.agents.items():
        state = states[client_id]
        
        # Get action without noise
        action = agent.get_action(state, add_noise=False)
        print(f"Agent {client_id} action (no noise): {action}")
        
        # Get action with noise
        action_noise = agent.get_action(state, add_noise=True)
        print(f"Agent {client_id} action (with noise): {action_noise}")
        
        # Test action normalization
        normalized_action = env.state_manager.normalize_action(action, env.clients[client_id])
        print(f"Agent {client_id} normalized action: {normalized_action}")


def test_task_scheduling():
    """Test task scheduling functionality"""
    print("\n=== Testing Task Scheduling ===")
    
    config = create_test_config()
    env = MADDPGEnvironment(config)
    
    # Reset environment
    states = env.reset()
    
    # Run a few steps to generate tasks
    for _ in range(5):
        next_states, rewards, dones, info = env.step()
        
        if info.get('event_type') == 'arrival':
            client_id = info.get('client_id')
            task_id = info.get('task_id')
            
            if client_id is not None and task_id is not None:
                print(f"Task {task_id} arrived at client {client_id}")
                
                # Get agent action for this task
                action = env.get_agent_action_for_task(client_id, task_id)
                print(f"Agent action: {action}")
                
                # Check client's task queue
                client = env.clients[client_id]
                task = client.get_task_by_id(task_id)
                if task:
                    print(f"Task details: deadline={task.deadline:.1f}, "
                          f"target_acc={task.target_accuracy:.2f}, "
                          f"model_size={task.model_size:.1f}MB")
                
                # Check resource manager
                resource_usage = client.resource_manager.get_current_resource_usage(env.current_time)
                print(f"Resource usage: {resource_usage}")
                
                break
        
        if all(dones.values()):
            break
        
        states = next_states


def test_training_step():
    """Test agent training"""
    print("\n=== Testing Agent Training ===")
    
    config = create_test_config()
    env = MADDPGEnvironment(config)
    
    # Reset environment
    states = env.reset()
    
    # Run episode to collect some experiences
    for _ in range(10):
        next_states, rewards, dones, info = env.step()
        
        # Add dummy experiences to agent buffers
        for client_id in env.client_ids:
            if client_id in states and client_id in next_states:
                agent = env.agents[client_id]
                
                dummy_action = {
                    'selected': True,
                    'computation_ratio': np.random.uniform(0.1, 1.0),
                    'bandwidth': np.random.uniform(5.0, 50.0),
                    'power': np.random.uniform(1.0, 10.0),
                    'timing_offset': np.random.uniform(-1.0, 1.0)
                }
                
                agent.add_experience(
                    state=states[client_id],
                    action=dummy_action,
                    reward=rewards[client_id],
                    next_state=next_states[client_id],
                    done=dones[client_id]
                )
        
        states = next_states
        
        if all(dones.values()):
            break
    
    # Check buffer sizes
    for client_id, agent in env.agents.items():
        buffer_size = len(agent.replay_buffer)
        print(f"Agent {client_id} buffer size: {buffer_size}")
    
    # Try training (may not work if buffer is too small)
    try:
        training_results = env.train_agents()
        print("Training results:", training_results)
    except Exception as e:
        print(f"Training failed (expected for small buffer): {e}")


def main():
    """Run all tests"""
    print("MADDPG Multi-Task FL System - Quick Test")
    print("=" * 50)
    
    try:
        # Test 1: Single episode
        test_single_episode()
        
        # Test 2: Agent actions
        test_agent_actions()
        
        # Test 3: Task scheduling
        test_task_scheduling()
        
        # Test 4: Training step
        test_training_step()
        
        print("\n" + "=" * 50)
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
