"""
Main training script for MADDPG Multi-Task Federated Learning
"""
import argparse
import os
import time
import torch
import numpy as np
from typing import Dict, Any

from src.environment.maddpg_env import MADDPGEnvironment
from src.utils.config_loader import load_config, validate_config
from src.utils.logger import Logger
from src.utils.visualization import create_performance_dashboard


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='MADDPG Multi-Task FL Training')
    
    parser.add_argument('--config', type=str, default='config/default.yaml',
                       help='Path to configuration file')
    parser.add_argument('--log-dir', type=str, default='logs',
                       help='Directory for logging')
    parser.add_argument('--save-dir', type=str, default='models',
                       help='Directory for saving models')
    parser.add_argument('--experiment-name', type=str, default=None,
                       help='Name of the experiment')
    parser.add_argument('--resume', type=str, default=None,
                       help='Path to resume training from')
    parser.add_argument('--eval-only', action='store_true',
                       help='Run evaluation only')
    parser.add_argument('--render', action='store_true',
                       help='Render environment during training')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    return parser.parse_args()


def set_random_seeds(seed: int):
    """Set random seeds for reproducibility"""
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)


def train_maddpg(config: Dict[str, Any], logger: Logger, args) -> Dict[str, Any]:
    """
    Main training loop for MADDPG agents
    
    Args:
        config: Configuration dictionary
        logger: Logger instance
        args: Command line arguments
        
    Returns:
        Dict: Training results
    """
    # Initialize environment
    env = MADDPGEnvironment(config)
    
    # Training parameters
    num_episodes = config['training']['num_episodes']
    save_interval = config['training']['save_interval']
    log_interval = config['training']['log_interval']
    
    # Resume training if specified
    start_episode = 0
    if args.resume:
        try:
            env.load_agents(args.resume)
            # Extract episode number from filename if possible
            if 'episode_' in args.resume:
                start_episode = int(args.resume.split('episode_')[1].split('_')[0])
            print(f"Resumed training from {args.resume}, starting at episode {start_episode}")
        except Exception as e:
            print(f"Failed to resume training: {e}")
            return {}
    
    # Training loop
    training_results = {
        'episodes_completed': 0,
        'total_training_time': 0,
        'best_performance': 0,
        'final_stats': {}
    }
    
    training_start_time = time.time()
    
    for episode in range(start_episode, num_episodes):
        logger.log_episode_start(episode)
        
        # Reset environment
        states = env.reset()
        episode_done = False
        step_count = 0
        
        # Episode loop
        while not episode_done:
            # Environment step (event-driven)
            next_states, rewards, dones, info = env.step()
            
            # Store experiences for all agents
            for client_id in env.client_ids:
                if client_id in states and client_id in next_states:
                    agent = env.agents[client_id]
                    
                    # Create dummy action for experience storage
                    # In practice, this would be the actual action taken
                    dummy_action = {
                        'selected': True,
                        'computation_ratio': 0.5,
                        'bandwidth': 25.0,
                        'power': 5.0,
                        'timing_offset': 0.0
                    }
                    
                    agent.add_experience(
                        state=states[client_id],
                        action=dummy_action,
                        reward=rewards[client_id],
                        next_state=next_states[client_id],
                        done=dones[client_id]
                    )
            
            # Update states
            states = next_states
            
            # Check if episode is done
            episode_done = all(dones.values())
            step_count += 1
            
            # Render if requested
            if args.render and step_count % 10 == 0:
                env.render()
        
        # Train agents
        training_losses = env.train_agents()
        logger.log_training_step(episode, step_count, training_losses)
        
        # Get episode statistics
        episode_stats = env.get_episode_statistics()
        logger.log_episode_end(episode, episode_stats)
        
        # Log system metrics
        system_metrics = {
            'completion_rate': episode_stats.get('task_stats', {}).get('completion_rate', 0),
            'energy_efficiency': episode_stats.get('total_tasks_completed', 0) / max(1, episode_stats.get('total_energy_consumed', 1)),
            'avg_agent_reward': np.mean(list(episode_stats.get('avg_rewards', {}).values()))
        }
        logger.log_system_metrics(episode, system_metrics)
        
        # Save models periodically
        if (episode + 1) % save_interval == 0:
            save_path = os.path.join(args.save_dir, f"episode_{episode}")
            os.makedirs(args.save_dir, exist_ok=True)
            env.save_agents(save_path)
            print(f"Models saved at episode {episode}")
        
        # Print progress
        if (episode + 1) % log_interval == 0:
            logger.print_training_progress(episode + 1, num_episodes)
        
        # Update training results
        training_results['episodes_completed'] = episode + 1
        
        # Track best performance
        current_performance = system_metrics['completion_rate']
        if current_performance > training_results['best_performance']:
            training_results['best_performance'] = current_performance
            # Save best model
            best_save_path = os.path.join(args.save_dir, "best_model")
            os.makedirs(args.save_dir, exist_ok=True)
            env.save_agents(best_save_path)
    
    # Final statistics
    training_results['total_training_time'] = time.time() - training_start_time
    training_results['final_stats'] = env.get_episode_statistics()
    
    # Save final models
    final_save_path = os.path.join(args.save_dir, "final_model")
    os.makedirs(args.save_dir, exist_ok=True)
    env.save_agents(final_save_path)
    
    return training_results


def evaluate_model(config: Dict[str, Any], model_path: str, logger: Logger, args) -> Dict[str, Any]:
    """
    Evaluate trained model
    
    Args:
        config: Configuration dictionary
        model_path: Path to trained model
        logger: Logger instance
        args: Command line arguments
        
    Returns:
        Dict: Evaluation results
    """
    # Initialize environment
    env = MADDPGEnvironment(config)
    
    # Load trained model
    try:
        env.load_agents(model_path)
        print(f"Loaded model from {model_path}")
    except Exception as e:
        print(f"Failed to load model: {e}")
        return {}
    
    # Evaluation parameters
    num_eval_episodes = 10
    eval_results = []
    
    print(f"Running evaluation for {num_eval_episodes} episodes...")
    
    for episode in range(num_eval_episodes):
        print(f"Evaluation episode {episode + 1}/{num_eval_episodes}")
        
        # Reset environment
        states = env.reset()
        episode_done = False
        
        # Episode loop (no training)
        while not episode_done:
            # Environment step
            next_states, rewards, dones, info = env.step()
            
            # Update states
            states = next_states
            episode_done = all(dones.values())
            
            # Render if requested
            if args.render:
                env.render()
        
        # Collect episode statistics
        episode_stats = env.get_episode_statistics()
        eval_results.append(episode_stats)
        
        # Print episode summary
        completion_rate = episode_stats.get('task_stats', {}).get('completion_rate', 0)
        energy = episode_stats.get('total_energy_consumed', 0)
        print(f"  Completion rate: {completion_rate:.3f}, Energy: {energy:.2f}")
    
    # Calculate evaluation metrics
    avg_completion_rate = np.mean([ep.get('task_stats', {}).get('completion_rate', 0) for ep in eval_results])
    avg_energy = np.mean([ep.get('total_energy_consumed', 0) for ep in eval_results])
    avg_efficiency = np.mean([
        ep.get('total_tasks_completed', 0) / max(1, ep.get('total_energy_consumed', 1))
        for ep in eval_results
    ])
    
    evaluation_summary = {
        'num_episodes': num_eval_episodes,
        'avg_completion_rate': avg_completion_rate,
        'avg_energy_consumption': avg_energy,
        'avg_energy_efficiency': avg_efficiency,
        'episode_results': eval_results
    }
    
    print(f"\nEvaluation Summary:")
    print(f"  Average completion rate: {avg_completion_rate:.3f}")
    print(f"  Average energy consumption: {avg_energy:.2f}")
    print(f"  Average energy efficiency: {avg_efficiency:.3f}")
    
    return evaluation_summary


def main():
    """Main function"""
    args = parse_arguments()
    
    # Set random seeds
    set_random_seeds(args.seed)
    
    # Load configuration
    try:
        config = load_config(args.config)
        if not validate_config(config):
            print("Configuration validation failed")
            return
    except Exception as e:
        print(f"Failed to load configuration: {e}")
        return
    
    # Initialize logger
    logger = Logger(args.log_dir, args.experiment_name)
    
    # Print configuration
    print("=== MADDPG Multi-Task Federated Learning ===")
    print(f"Configuration: {args.config}")
    print(f"Clients: {config['clients']['num_clients']}")
    print(f"Episodes: {config['training']['num_episodes']}")
    print(f"Seed: {args.seed}")
    print(f"Device: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    
    if args.eval_only:
        # Evaluation mode
        if not args.resume:
            print("Error: --resume must be specified for evaluation")
            return
        
        results = evaluate_model(config, args.resume, logger, args)
        
        # Save evaluation results
        logger.save_logs()
        
    else:
        # Training mode
        results = train_maddpg(config, logger, args)
        
        # Save logs
        logger.save_logs()
        
        # Create performance dashboard
        if logger.episode_logs:
            dashboard_dir = os.path.join(logger.experiment_dir, 'dashboard')
            create_performance_dashboard(logger.episode_logs, dashboard_dir)
        
        print(f"\nTraining completed!")
        print(f"Episodes: {results.get('episodes_completed', 0)}")
        print(f"Training time: {results.get('total_training_time', 0)/3600:.2f} hours")
        print(f"Best performance: {results.get('best_performance', 0):.3f}")
        print(f"Logs saved to: {logger.experiment_dir}")


if __name__ == "__main__":
    main()
