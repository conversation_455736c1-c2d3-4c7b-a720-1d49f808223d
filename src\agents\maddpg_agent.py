"""
MADDPG Agent for Multi-Task Federated Learning
"""
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List, Tuple, Any, Optional

from .networks import Actor, Critic, create_networks
from .replay_buffer import ReplayBuffer


class MADDPGAgent:
    """
    Multi-Agent Deep Deterministic Policy Gradient Agent
    
    Each agent outputs 5-parameter actions for FL task scheduling:
    - Selected_ij: Binary decision to execute task j
    - q_ij: Computation resource ratio (0-1)
    - B_ij: Bandwidth allocation (Mbps)
    - P_ij: Power consumption (Watts)
    - start_time_ij: Execution start time (relative offset)
    """
    
    def __init__(self, agent_id: int, state_dim: int, action_dim: int, config: Dict):
        self.agent_id = agent_id
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.config = config
        
        # Hyperparameters
        self.actor_lr = config['maddpg']['actor_lr']
        self.critic_lr = config['maddpg']['critic_lr']
        self.gamma = config['maddpg']['gamma']
        self.tau = config['maddpg']['tau']
        self.batch_size = config['maddpg']['batch_size']
        
        # Create networks
        self.actor, self.critic, self.target_actor, self.target_critic = create_networks(
            state_dim, action_dim, config['maddpg']
        )
        
        # Optimizers
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=self.actor_lr)
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=self.critic_lr)
        
        # Replay buffer
        buffer_size = config['maddpg']['buffer_size']
        self.replay_buffer = ReplayBuffer(buffer_size, state_dim, action_dim)
        
        # Exploration parameters
        self.exploration_noise = 0.1
        self.noise_decay = 0.995
        self.min_noise = 0.01
        
        # Training statistics
        self.training_step = 0
        self.actor_losses = []
        self.critic_losses = []
        
        # Device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self._move_to_device()
    
    def _move_to_device(self):
        """Move networks to device"""
        self.actor.to(self.device)
        self.critic.to(self.device)
        self.target_actor.to(self.device)
        self.target_critic.to(self.device)
    
    def get_action(self, state: np.ndarray, task_context: Dict = None, 
                   add_noise: bool = True) -> Dict[str, Any]:
        """
        Get action for current state
        
        Args:
            state: Current state observation
            task_context: Additional task context (task_id, deadline, etc.)
            add_noise: Whether to add exploration noise
            
        Returns:
            Dict: Action with 5 parameters
        """
        # Convert state to tensor
        if isinstance(state, np.ndarray):
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        else:
            state_tensor = torch.FloatTensor([state]).to(self.device)
        
        # Get action from actor network
        action_dict = self.actor.get_action(state_tensor, add_noise, self.exploration_noise)
        
        # Process timing offset based on task context
        if task_context:
            current_time = task_context.get('current_time', 0.0)
            deadline = task_context.get('deadline', float('inf'))
            
            # Convert relative timing offset to absolute start time
            max_delay = min(100.0, (deadline - current_time) * 0.5)  # Max 50% of remaining time
            timing_offset = action_dict['timing_offset']  # [-1, 1]
            
            # Map to [current_time, current_time + max_delay]
            start_time = current_time + max(0, (timing_offset + 1) * 0.5 * max_delay)
            action_dict['start_time'] = start_time
        else:
            action_dict['start_time'] = action_dict.get('timing_offset', 0.0)
        
        return action_dict
    
    def action_to_tensor(self, action: Dict[str, Any]) -> torch.Tensor:
        """
        Convert action dictionary to tensor for training
        
        Args:
            action: Action dictionary
            
        Returns:
            torch.Tensor: Action tensor
        """
        # Convert action components to tensor
        action_values = [
            float(action.get('selected', 0)),
            action.get('computation_ratio', 0.0),
            action.get('bandwidth', 0.0),
            action.get('power', 0.0),
            action.get('timing_offset', 0.0)
        ]
        
        return torch.FloatTensor(action_values)
    
    def add_experience(self, state: np.ndarray, action: Dict[str, Any], 
                      reward: float, next_state: np.ndarray, done: bool):
        """
        Add experience to replay buffer
        
        Args:
            state: Current state
            action: Action taken
            reward: Reward received
            next_state: Next state
            done: Whether episode is done
        """
        action_tensor = self.action_to_tensor(action).numpy()
        self.replay_buffer.add(state, action_tensor, reward, next_state, done)
    
    def update(self, other_agents: List['MADDPGAgent'] = None) -> Dict[str, float]:
        """
        Update actor and critic networks
        
        Args:
            other_agents: List of other agents for centralized training
            
        Returns:
            Dict: Training losses
        """
        if not self.replay_buffer.is_ready(self.batch_size):
            return {'actor_loss': 0.0, 'critic_loss': 0.0}
        
        # Sample batch
        batch = self.replay_buffer.sample(self.batch_size)
        states = batch['states'].to(self.device)
        actions = batch['actions'].to(self.device)
        rewards = batch['rewards'].to(self.device)
        next_states = batch['next_states'].to(self.device)
        dones = batch['dones'].to(self.device)
        
        # Update critic
        critic_loss = self._update_critic(states, actions, rewards, next_states, dones)
        
        # Update actor
        actor_loss = self._update_actor(states)
        
        # Soft update target networks
        self._soft_update_targets()
        
        # Update exploration noise
        self.exploration_noise = max(self.min_noise, 
                                   self.exploration_noise * self.noise_decay)
        
        self.training_step += 1
        
        # Store losses
        self.actor_losses.append(actor_loss)
        self.critic_losses.append(critic_loss)
        
        return {
            'actor_loss': actor_loss,
            'critic_loss': critic_loss,
            'exploration_noise': self.exploration_noise
        }
    
    def _update_critic(self, states: torch.Tensor, actions: torch.Tensor,
                      rewards: torch.Tensor, next_states: torch.Tensor,
                      dones: torch.Tensor) -> float:
        """Update critic network"""
        # Calculate target Q-values
        with torch.no_grad():
            # Get next actions from target actor
            next_action_dict = self.target_actor(next_states)
            next_actions = torch.cat([
                next_action_dict['selection_prob'],
                next_action_dict['computation_ratio'],
                next_action_dict['bandwidth'],
                next_action_dict['power'],
                next_action_dict['timing_offset']
            ], dim=1)
            
            # Calculate target Q-values
            target_q = self.target_critic(next_states, next_actions)
            target_q = rewards + (self.gamma * target_q * (~dones))
        
        # Current Q-values
        current_q = self.critic(states, actions)
        
        # Critic loss
        critic_loss = nn.MSELoss()(current_q, target_q)
        
        # Update critic
        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.critic.parameters(), 1.0)
        self.critic_optimizer.step()
        
        return critic_loss.item()
    
    def _update_actor(self, states: torch.Tensor) -> float:
        """Update actor network"""
        # Get actions from current actor
        action_dict = self.actor(states)
        actions = torch.cat([
            action_dict['selection_prob'],
            action_dict['computation_ratio'],
            action_dict['bandwidth'],
            action_dict['power'],
            action_dict['timing_offset']
        ], dim=1)
        
        # Actor loss (negative Q-value)
        actor_loss = -self.critic(states, actions).mean()
        
        # Update actor
        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        torch.nn.utils.clip_grad_norm_(self.actor.parameters(), 1.0)
        self.actor_optimizer.step()
        
        return actor_loss.item()
    
    def _soft_update_targets(self):
        """Soft update target networks"""
        for target_param, param in zip(self.target_actor.parameters(), 
                                     self.actor.parameters()):
            target_param.data.copy_(self.tau * param.data + 
                                  (1 - self.tau) * target_param.data)
        
        for target_param, param in zip(self.target_critic.parameters(), 
                                     self.critic.parameters()):
            target_param.data.copy_(self.tau * param.data + 
                                  (1 - self.tau) * target_param.data)
    
    def save_model(self, filepath: str):
        """Save agent model"""
        torch.save({
            'actor_state_dict': self.actor.state_dict(),
            'critic_state_dict': self.critic.state_dict(),
            'target_actor_state_dict': self.target_actor.state_dict(),
            'target_critic_state_dict': self.target_critic.state_dict(),
            'actor_optimizer_state_dict': self.actor_optimizer.state_dict(),
            'critic_optimizer_state_dict': self.critic_optimizer.state_dict(),
            'training_step': self.training_step,
            'exploration_noise': self.exploration_noise
        }, filepath)
    
    def load_model(self, filepath: str):
        """Load agent model"""
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.actor.load_state_dict(checkpoint['actor_state_dict'])
        self.critic.load_state_dict(checkpoint['critic_state_dict'])
        self.target_actor.load_state_dict(checkpoint['target_actor_state_dict'])
        self.target_critic.load_state_dict(checkpoint['target_critic_state_dict'])
        self.actor_optimizer.load_state_dict(checkpoint['actor_optimizer_state_dict'])
        self.critic_optimizer.load_state_dict(checkpoint['critic_optimizer_state_dict'])
        self.training_step = checkpoint['training_step']
        self.exploration_noise = checkpoint['exploration_noise']
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get agent statistics"""
        return {
            'agent_id': self.agent_id,
            'training_step': self.training_step,
            'exploration_noise': self.exploration_noise,
            'buffer_size': len(self.replay_buffer),
            'avg_actor_loss': np.mean(self.actor_losses[-100:]) if self.actor_losses else 0.0,
            'avg_critic_loss': np.mean(self.critic_losses[-100:]) if self.critic_losses else 0.0,
            'buffer_stats': self.replay_buffer.get_statistics()
        }
