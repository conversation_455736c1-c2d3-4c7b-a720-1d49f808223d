"""
Neural Network Architectures for MADDPG
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class Actor(nn.Module):
    """
    Actor network for MADDPG agent
    
    Outputs 5-parameter actions: (Selected_ij, q_ij, B_ij, P_ij, start_time_ij)
    """
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 128,
                 max_bandwidth: float = 50.0, max_power: float = 10.0):
        super(Actor, self).__init__()
        
        self.max_bandwidth = max_bandwidth
        self.max_power = max_power
        
        # Shared layers
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        
        # Separate heads for different action components
        self.selection_head = nn.Linear(hidden_dim, 1)  # Selected_ij (binary)
        self.computation_head = nn.Linear(hidden_dim, 1)  # q_ij (0-1)
        self.bandwidth_head = nn.Linear(hidden_dim, 1)  # B_ij (0-max_bandwidth)
        self.power_head = nn.Linear(hidden_dim, 1)  # P_ij (0-max_power)
        self.timing_head = nn.Linear(hidden_dim, 1)  # start_time_ij (relative)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            nn.init.constant_(module.bias, 0.0)
    
    def forward(self, state):
        """
        Forward pass
        
        Args:
            state: Input state tensor
            
        Returns:
            Dict: Action components
        """
        # Shared feature extraction
        x = F.relu(self.fc1(state))
        x = F.relu(self.fc2(x))
        
        # Action components
        selection_logit = self.selection_head(x)
        selection_prob = torch.sigmoid(selection_logit)
        
        computation_ratio = torch.sigmoid(self.computation_head(x))
        bandwidth = torch.sigmoid(self.bandwidth_head(x)) * self.max_bandwidth
        power = torch.sigmoid(self.power_head(x)) * self.max_power
        timing_offset = torch.tanh(self.timing_head(x))  # Relative timing (-1 to 1)
        
        return {
            'selection_prob': selection_prob,
            'computation_ratio': computation_ratio,
            'bandwidth': bandwidth,
            'power': power,
            'timing_offset': timing_offset
        }
    
    def get_action(self, state, add_noise=True, noise_scale=0.1):
        """
        Get action with optional noise for exploration
        
        Args:
            state: Input state
            add_noise: Whether to add exploration noise
            noise_scale: Scale of exploration noise
            
        Returns:
            Dict: Action with discrete and continuous components
        """
        with torch.no_grad():
            if isinstance(state, np.ndarray):
                state = torch.FloatTensor(state).unsqueeze(0)
            
            action_dict = self.forward(state)
            
            # Sample binary selection
            selection = torch.bernoulli(action_dict['selection_prob']).item() > 0.5
            
            # Get continuous actions
            computation_ratio = action_dict['computation_ratio'].item()
            bandwidth = action_dict['bandwidth'].item()
            power = action_dict['power'].item()
            timing_offset = action_dict['timing_offset'].item()
            
            # Add exploration noise if requested
            if add_noise:
                computation_ratio = np.clip(
                    computation_ratio + np.random.normal(0, noise_scale), 0, 1
                )
                bandwidth = np.clip(
                    bandwidth + np.random.normal(0, noise_scale * self.max_bandwidth),
                    0, self.max_bandwidth
                )
                power = np.clip(
                    power + np.random.normal(0, noise_scale * self.max_power),
                    0, self.max_power
                )
                timing_offset = np.clip(
                    timing_offset + np.random.normal(0, noise_scale), -1, 1
                )
            
            return {
                'selected': selection,
                'computation_ratio': computation_ratio,
                'bandwidth': bandwidth,
                'power': power,
                'timing_offset': timing_offset
            }


class Critic(nn.Module):
    """
    Critic network for MADDPG agent
    
    Evaluates state-action pairs for Q-learning
    """
    
    def __init__(self, state_dim: int, action_dim: int, hidden_dim: int = 128):
        super(Critic, self).__init__()
        
        # State processing
        self.state_fc1 = nn.Linear(state_dim, hidden_dim)
        self.state_fc2 = nn.Linear(hidden_dim, hidden_dim)
        
        # Action processing
        self.action_fc1 = nn.Linear(action_dim, hidden_dim)
        
        # Combined processing
        self.combined_fc1 = nn.Linear(hidden_dim * 2, hidden_dim)
        self.combined_fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.q_head = nn.Linear(hidden_dim, 1)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.xavier_uniform_(module.weight)
            nn.init.constant_(module.bias, 0.0)
    
    def forward(self, state, action):
        """
        Forward pass
        
        Args:
            state: State tensor
            action: Action tensor
            
        Returns:
            torch.Tensor: Q-value
        """
        # Process state
        state_features = F.relu(self.state_fc1(state))
        state_features = F.relu(self.state_fc2(state_features))
        
        # Process action
        action_features = F.relu(self.action_fc1(action))
        
        # Combine state and action features
        combined = torch.cat([state_features, action_features], dim=-1)
        combined = F.relu(self.combined_fc1(combined))
        combined = F.relu(self.combined_fc2(combined))
        
        # Output Q-value
        q_value = self.q_head(combined)
        
        return q_value


def create_networks(state_dim: int, action_dim: int, config: dict):
    """
    Create actor and critic networks
    
    Args:
        state_dim: Dimension of state space
        action_dim: Dimension of action space
        config: Configuration dictionary
        
    Returns:
        Tuple: (actor, critic, target_actor, target_critic)
    """
    hidden_dim = config.get('hidden_dim', 128)
    max_bandwidth = config.get('total_bandwidth_capacity', 50.0)
    max_power = config.get('max_power_consumption', 10.0)
    
    # Create networks
    actor = Actor(state_dim, action_dim, hidden_dim, max_bandwidth, max_power)
    critic = Critic(state_dim, action_dim, hidden_dim)
    
    # Create target networks
    target_actor = Actor(state_dim, action_dim, hidden_dim, max_bandwidth, max_power)
    target_critic = Critic(state_dim, action_dim, hidden_dim)
    
    # Initialize target networks with same weights
    target_actor.load_state_dict(actor.state_dict())
    target_critic.load_state_dict(critic.state_dict())
    
    return actor, critic, target_actor, target_critic
