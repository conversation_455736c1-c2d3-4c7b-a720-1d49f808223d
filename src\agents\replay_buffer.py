"""
Replay Buffer for MADDPG
"""
import numpy as np
import torch
from collections import deque
import random
from typing import List, Tu<PERSON>, Dict, Any


class ReplayBuffer:
    """
    Replay buffer for storing and sampling experience tuples
    """
    
    def __init__(self, capacity: int, state_dim: int, action_dim: int):
        self.capacity = capacity
        self.state_dim = state_dim
        self.action_dim = action_dim
        
        # Use deque for efficient FIFO operations
        self.buffer = deque(maxlen=capacity)
        
        # Track buffer statistics
        self.total_added = 0
    
    def add(self, state: np.ndarray, action: np.ndarray, reward: float,
            next_state: np.ndarray, done: bool):
        """
        Add an experience tuple to the buffer
        
        Args:
            state: Current state
            action: Action taken
            reward: Reward received
            next_state: Next state
            done: Whether episode is done
        """
        experience = {
            'state': state.copy() if isinstance(state, np.ndarray) else state,
            'action': action.copy() if isinstance(action, np.ndarray) else action,
            'reward': reward,
            'next_state': next_state.copy() if isinstance(next_state, np.ndarray) else next_state,
            'done': done
        }
        
        self.buffer.append(experience)
        self.total_added += 1
    
    def sample(self, batch_size: int) -> Dict[str, torch.Tensor]:
        """
        Sample a batch of experiences
        
        Args:
            batch_size: Number of experiences to sample
            
        Returns:
            Dict: Batch of experiences as tensors
        """
        if len(self.buffer) < batch_size:
            raise ValueError(f"Buffer has {len(self.buffer)} experiences, cannot sample {batch_size}")
        
        # Sample random experiences
        experiences = random.sample(self.buffer, batch_size)
        
        # Convert to tensors
        states = torch.FloatTensor([exp['state'] for exp in experiences])
        actions = torch.FloatTensor([exp['action'] for exp in experiences])
        rewards = torch.FloatTensor([exp['reward'] for exp in experiences]).unsqueeze(1)
        next_states = torch.FloatTensor([exp['next_state'] for exp in experiences])
        dones = torch.BoolTensor([exp['done'] for exp in experiences]).unsqueeze(1)
        
        return {
            'states': states,
            'actions': actions,
            'rewards': rewards,
            'next_states': next_states,
            'dones': dones
        }
    
    def __len__(self):
        return len(self.buffer)
    
    def is_ready(self, batch_size: int) -> bool:
        """Check if buffer has enough experiences for sampling"""
        return len(self.buffer) >= batch_size
    
    def clear(self):
        """Clear the buffer"""
        self.buffer.clear()
        self.total_added = 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get buffer statistics"""
        return {
            'size': len(self.buffer),
            'capacity': self.capacity,
            'total_added': self.total_added,
            'utilization': len(self.buffer) / self.capacity
        }


class MultiAgentReplayBuffer:
    """
    Multi-agent replay buffer for MADDPG
    
    Stores experiences for multiple agents with centralized training
    """
    
    def __init__(self, capacity: int, num_agents: int, state_dims: List[int], 
                 action_dims: List[int]):
        self.capacity = capacity
        self.num_agents = num_agents
        self.state_dims = state_dims
        self.action_dims = action_dims
        
        # Individual buffers for each agent
        self.agent_buffers = [
            ReplayBuffer(capacity, state_dims[i], action_dims[i])
            for i in range(num_agents)
        ]
        
        # Global experience buffer for centralized training
        self.global_buffer = deque(maxlen=capacity)
        self.total_added = 0
    
    def add_experience(self, agent_id: int, state: np.ndarray, action: np.ndarray,
                      reward: float, next_state: np.ndarray, done: bool,
                      global_state: np.ndarray = None, global_next_state: np.ndarray = None):
        """
        Add experience for a specific agent
        
        Args:
            agent_id: ID of the agent
            state: Agent's local state
            action: Agent's action
            reward: Agent's reward
            next_state: Agent's next local state
            done: Whether episode is done
            global_state: Global state (optional)
            global_next_state: Global next state (optional)
        """
        # Add to individual agent buffer
        self.agent_buffers[agent_id].add(state, action, reward, next_state, done)
        
        # Add to global buffer if global states are provided
        if global_state is not None and global_next_state is not None:
            global_experience = {
                'agent_id': agent_id,
                'local_state': state.copy() if isinstance(state, np.ndarray) else state,
                'action': action.copy() if isinstance(action, np.ndarray) else action,
                'reward': reward,
                'local_next_state': next_state.copy() if isinstance(next_state, np.ndarray) else next_state,
                'global_state': global_state.copy() if isinstance(global_state, np.ndarray) else global_state,
                'global_next_state': global_next_state.copy() if isinstance(global_next_state, np.ndarray) else global_next_state,
                'done': done
            }
            self.global_buffer.append(global_experience)
        
        self.total_added += 1
    
    def sample_agent_batch(self, agent_id: int, batch_size: int) -> Dict[str, torch.Tensor]:
        """Sample batch for a specific agent"""
        return self.agent_buffers[agent_id].sample(batch_size)
    
    def sample_global_batch(self, batch_size: int) -> Dict[str, torch.Tensor]:
        """
        Sample global batch for centralized training
        
        Args:
            batch_size: Number of experiences to sample
            
        Returns:
            Dict: Batch of global experiences
        """
        if len(self.global_buffer) < batch_size:
            raise ValueError(f"Global buffer has {len(self.global_buffer)} experiences, cannot sample {batch_size}")
        
        experiences = random.sample(self.global_buffer, batch_size)
        
        # Group by agent
        agent_experiences = {i: [] for i in range(self.num_agents)}
        for exp in experiences:
            agent_experiences[exp['agent_id']].append(exp)
        
        # Convert to tensors
        batch = {}
        for agent_id in range(self.num_agents):
            if agent_experiences[agent_id]:
                agent_exps = agent_experiences[agent_id]
                batch[f'agent_{agent_id}'] = {
                    'local_states': torch.FloatTensor([exp['local_state'] for exp in agent_exps]),
                    'actions': torch.FloatTensor([exp['action'] for exp in agent_exps]),
                    'rewards': torch.FloatTensor([exp['reward'] for exp in agent_exps]).unsqueeze(1),
                    'local_next_states': torch.FloatTensor([exp['local_next_state'] for exp in agent_exps]),
                    'global_states': torch.FloatTensor([exp['global_state'] for exp in agent_exps]),
                    'global_next_states': torch.FloatTensor([exp['global_next_state'] for exp in agent_exps]),
                    'dones': torch.BoolTensor([exp['done'] for exp in agent_exps]).unsqueeze(1)
                }
        
        return batch
    
    def is_ready(self, batch_size: int) -> bool:
        """Check if all agent buffers are ready for sampling"""
        return all(buffer.is_ready(batch_size) for buffer in self.agent_buffers)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics for all buffers"""
        agent_stats = [buffer.get_statistics() for buffer in self.agent_buffers]
        
        return {
            'total_added': self.total_added,
            'global_buffer_size': len(self.global_buffer),
            'agent_buffers': agent_stats,
            'min_agent_size': min(len(buffer) for buffer in self.agent_buffers),
            'max_agent_size': max(len(buffer) for buffer in self.agent_buffers)
        }
