"""
FL Client Implementation with Resource Management and Task Scheduling
"""
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from collections import deque

from .resource_manager import ResourceManager, ScheduleEntry
from ..tasks.fl_task import FLTask


class Client:
    """
    Federated Learning Client with MADDPG agent integration
    
    Each client manages:
    - Task queue (task_queue_i): Tasks assigned to this client
    - Schedule queue (schedule_queue_i): Planned execution timeline
    - Resource allocation: Computation, bandwidth, and power management
    """
    
    def __init__(self, client_id: int, config: Dict):
        self.client_id = client_id
        self.config = config
        
        # Resource capacities
        self.total_computation = config['clients']['total_computation_capacity']
        self.total_bandwidth = config['clients']['total_bandwidth_capacity']
        self.max_power = config['clients']['max_power_consumption']
        
        # Resource manager
        self.resource_manager = ResourceManager(
            client_id=client_id,
            total_computation=self.total_computation,
            total_bandwidth=self.total_bandwidth,
            max_power=self.max_power
        )
        
        # Task management
        self.task_queue: List[FLTask] = []  # Active tasks assigned to this client
        self.completed_tasks: List[int] = []  # Completed task IDs
        
        # Performance tracking
        self.total_energy_consumed = 0.0
        self.total_tasks_completed = 0
        self.total_rounds_executed = 0
        
        # Current state for agent observation
        self.current_time = 0.0
        self.last_action = None
        self.last_reward = 0.0
    
    def add_task(self, task: FLTask):
        """Add a new task to this client's queue"""
        self.task_queue.append(task)
        task.assigned_clients.append(self.client_id)
    
    def remove_task(self, task_id: int):
        """Remove a task from the client's queue"""
        self.task_queue = [task for task in self.task_queue if task.task_id != task_id]
        if task_id not in self.completed_tasks:
            self.completed_tasks.append(task_id)
    
    def get_task_by_id(self, task_id: int) -> Optional[FLTask]:
        """Get a task by its ID"""
        for task in self.task_queue:
            if task.task_id == task_id:
                return task
        return None
    
    def calculate_computation_time(self, task: FLTask, computation_ratio: float, 
                                 local_rounds: int = None) -> float:
        """
        Calculate computation time T_cmp_ij for a task round
        
        Args:
            task: FL task
            computation_ratio: q_ij (proportion of computation capacity)
            local_rounds: Number of local training rounds (L_ij)
            
        Returns:
            float: Computation time in time units
        """
        if local_rounds is None:
            # Default local rounds based on data size and task complexity
            data_size = task.data_sizes.get(self.client_id, 1000)
            local_rounds = max(1, int(data_size / 1000))  # 1 round per 1000 samples
        
        # Base computation time depends on data size and model complexity
        data_size = task.data_sizes.get(self.client_id, 1000)
        base_time = data_size * 0.001 * local_rounds  # Simplified model
        
        # Actual time depends on allocated computation resources
        if computation_ratio <= 0:
            return float('inf')
        
        actual_computation_capacity = self.total_computation * computation_ratio
        computation_time = base_time / actual_computation_capacity
        
        return computation_time
    
    def calculate_communication_time(self, task: FLTask, bandwidth: float) -> float:
        """
        Calculate communication time T_com_ij for model upload
        
        Args:
            task: FL task
            bandwidth: B_ij (allocated bandwidth in Mbps)
            
        Returns:
            float: Communication time in time units
        """
        if bandwidth <= 0:
            return float('inf')
        
        # Model size in MB, bandwidth in Mbps
        model_size_mb = task.model_size
        upload_time = model_size_mb * 8 / bandwidth  # Convert MB to Mbits, divide by Mbps
        
        return upload_time
    
    def calculate_total_execution_time(self, task: FLTask, computation_ratio: float,
                                     bandwidth: float, local_rounds: int = None) -> float:
        """
        Calculate total execution time G_ij = T_cmp_ij + T_com_ij
        
        Args:
            task: FL task
            computation_ratio: q_ij
            bandwidth: B_ij
            local_rounds: L_ij
            
        Returns:
            float: Total execution time
        """
        comp_time = self.calculate_computation_time(task, computation_ratio, local_rounds)
        comm_time = self.calculate_communication_time(task, bandwidth)
        return comp_time + comm_time
    
    def calculate_energy_consumption(self, computation_ratio: float, bandwidth: float,
                                   power: float, duration: float) -> float:
        """
        Calculate energy consumption for a task round

        Args:
            computation_ratio: q_ij
            bandwidth: B_ij
            power: P_ij (total power consumption)
            duration: G_ij (execution duration)

        Returns:
            float: Energy consumption in Joules
        """
        # Energy = Power × Time
        return power * duration

    def schedule_task_round(self, task_id: int, action: Dict) -> Tuple[bool, Optional[ScheduleEntry]]:
        """
        Schedule a task round based on agent action

        Args:
            task_id: ID of the task to schedule
            action: Agent action with keys: selected, computation_ratio, bandwidth, power, start_time

        Returns:
            Tuple[bool, Optional[ScheduleEntry]]: (success, schedule_entry)
        """
        # Extract action parameters
        selected = action.get('selected', False)
        if not selected:
            return False, None

        computation_ratio = action.get('computation_ratio', 0.0)
        bandwidth = action.get('bandwidth', 0.0)
        power = action.get('power', 0.0)
        start_time = action.get('start_time', self.current_time)

        # Get task
        task = self.get_task_by_id(task_id)
        if task is None:
            return False, None

        # Calculate execution duration
        duration = self.calculate_total_execution_time(task, computation_ratio, bandwidth)

        # Create schedule entry
        round_num = task.current_round + 1
        entry = ScheduleEntry(
            start_time=start_time,
            end_time=start_time + duration,
            task_id=task_id,
            round_num=round_num,
            computation_ratio=computation_ratio,
            bandwidth_allocation=bandwidth,
            power_consumption=power
        )

        # Try to add to schedule
        success = self.resource_manager.add_schedule_entry(entry)

        if success:
            # Calculate and track energy consumption
            energy = self.calculate_energy_consumption(
                computation_ratio, bandwidth, power, duration
            )
            self.total_energy_consumed += energy
            task.add_energy_consumption(energy)

            return True, entry

        return False, None

    def execute_scheduled_round(self, entry: ScheduleEntry) -> Dict:
        """
        Execute a scheduled task round and return results

        Args:
            entry: ScheduleEntry to execute

        Returns:
            Dict: Execution results including new accuracy
        """
        task = self.get_task_by_id(entry.task_id)
        if task is None:
            return {'success': False, 'error': 'Task not found'}

        # Simulate FL round execution
        # In a real implementation, this would involve actual model training

        # Calculate accuracy improvement based on allocated resources
        base_improvement = 0.02  # Base accuracy improvement per round

        # Resource quality affects improvement
        resource_factor = (entry.computation_ratio +
                          min(1.0, entry.bandwidth_allocation / self.total_bandwidth)) / 2

        # Data size affects improvement
        data_size = task.data_sizes.get(self.client_id, 1000)
        data_factor = min(1.0, data_size / 5000)  # Normalize to [0,1]

        # Calculate new accuracy
        improvement = base_improvement * resource_factor * data_factor
        new_accuracy = min(1.0, task.current_accuracy + improvement)

        # Update tracking
        self.total_rounds_executed += 1

        return {
            'success': True,
            'task_id': entry.task_id,
            'round_num': entry.round_num,
            'new_accuracy': new_accuracy,
            'improvement': improvement,
            'energy_consumed': self.calculate_energy_consumption(
                entry.computation_ratio, entry.bandwidth_allocation,
                entry.power_consumption, entry.duration
            ),
            'execution_time': entry.duration
        }

    def get_state_observation(self) -> Dict:
        """
        Get current state observation for the MADDPG agent

        Returns:
            Dict: State observation including tasks, resources, and schedule
        """
        # Task queue information
        task_info = []
        for task in self.task_queue:
            task_info.append({
                'task_id': task.task_id,
                'deadline': task.deadline,
                'remaining_time': task.remaining_time(self.current_time),
                'target_accuracy': task.target_accuracy,
                'current_accuracy': task.current_accuracy,
                'current_round': task.current_round,
                'max_rounds': task.max_global_rounds,
                'data_size': task.data_sizes.get(self.client_id, 0),
                'model_size': task.model_size,
                'urgency_score': task.get_urgency_score(self.current_time),
                'progress_ratio': task.get_progress_ratio()
            })

        # Resource utilization
        resource_usage = self.resource_manager.get_current_resource_usage(self.current_time)
        resource_stats = self.resource_manager.get_resource_utilization_stats()

        # Schedule information
        schedule_summary = self.resource_manager.get_schedule_summary()

        return {
            'client_id': self.client_id,
            'current_time': self.current_time,
            'tasks': task_info,
            'resource_usage': resource_usage,
            'resource_stats': resource_stats,
            'schedule': schedule_summary,
            'total_energy_consumed': self.total_energy_consumed,
            'total_tasks_completed': self.total_tasks_completed,
            'total_rounds_executed': self.total_rounds_executed,
            'last_reward': self.last_reward
        }

    def update_time(self, current_time: float):
        """Update current time and clean up completed schedule entries"""
        self.current_time = current_time
        self.resource_manager.remove_completed_entries(current_time)

    def get_performance_metrics(self) -> Dict:
        """Get performance metrics for this client"""
        return {
            'client_id': self.client_id,
            'total_energy_consumed': self.total_energy_consumed,
            'total_tasks_completed': self.total_tasks_completed,
            'total_rounds_executed': self.total_rounds_executed,
            'avg_energy_per_task': (self.total_energy_consumed / max(1, self.total_tasks_completed)),
            'avg_rounds_per_task': (self.total_rounds_executed / max(1, self.total_tasks_completed)),
            'resource_utilization': self.resource_manager.get_resource_utilization_stats(),
            'active_tasks': len(self.task_queue)
        }
