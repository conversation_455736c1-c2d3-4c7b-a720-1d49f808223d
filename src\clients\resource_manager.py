"""
Resource Management for FL Clients
"""
import numpy as np
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict


@dataclass
class ScheduleEntry:
    """Represents a scheduled task execution on a client"""
    start_time: float
    end_time: float
    task_id: int
    round_num: int
    computation_ratio: float  # q_ij
    bandwidth_allocation: float  # B_ij (Mbps)
    power_consumption: float  # P_ij (Watts)
    
    def __post_init__(self):
        assert self.start_time <= self.end_time, "Start time must be <= end time"
        assert 0 <= self.computation_ratio <= 1, "Computation ratio must be in [0,1]"
        assert self.bandwidth_allocation >= 0, "Bandwidth must be non-negative"
        assert self.power_consumption >= 0, "Power must be non-negative"
    
    @property
    def duration(self) -> float:
        return self.end_time - self.start_time
    
    def overlaps_with(self, other: 'ScheduleEntry') -> bool:
        """Check if this schedule entry overlaps with another"""
        return not (self.end_time <= other.start_time or other.end_time <= self.start_time)
    
    def __repr__(self):
        return (f"ScheduleEntry(t=[{self.start_time:.1f},{self.end_time:.1f}], "
                f"task={self.task_id}, round={self.round_num})")


class ResourceManager:
    """
    Manages computational and communication resources for a client
    """
    
    def __init__(self, client_id: int, total_computation: float, 
                 total_bandwidth: float, max_power: float):
        self.client_id = client_id
        self.total_computation = total_computation  # τ_i^total
        self.total_bandwidth = total_bandwidth      # q_i^total (Mbps)
        self.max_power = max_power                  # Maximum power consumption
        
        # Schedule queue - sorted by start time
        self.schedule_queue: List[ScheduleEntry] = []
        
        # Resource utilization tracking
        self.computation_history = []  # (time, utilization_ratio)
        self.bandwidth_history = []    # (time, bandwidth_usage)
        self.power_history = []        # (time, power_consumption)
        
        # Current resource usage (for real-time tracking)
        self.current_computation_usage = 0.0
        self.current_bandwidth_usage = 0.0
        self.current_power_usage = 0.0
    
    def add_schedule_entry(self, entry: ScheduleEntry) -> bool:
        """
        Add a new schedule entry if resources are available
        
        Args:
            entry: ScheduleEntry to add
            
        Returns:
            bool: True if successfully added, False if conflicts exist
        """
        # Check for time conflicts
        for existing in self.schedule_queue:
            if entry.overlaps_with(existing):
                return False
        
        # Check resource constraints at the scheduled time
        if not self._check_resource_availability(entry):
            return False
        
        # Add to schedule and sort by start time
        self.schedule_queue.append(entry)
        self.schedule_queue.sort(key=lambda x: x.start_time)
        
        return True
    
    def _check_resource_availability(self, entry: ScheduleEntry) -> bool:
        """Check if resources are available for the given entry"""
        # Get overlapping entries
        overlapping = [e for e in self.schedule_queue if entry.overlaps_with(e)]
        
        # Calculate total resource usage during the overlap period
        total_computation = sum(e.computation_ratio for e in overlapping)
        total_bandwidth = sum(e.bandwidth_allocation for e in overlapping)
        total_power = sum(e.power_consumption for e in overlapping)
        
        # Add the new entry's requirements
        total_computation += entry.computation_ratio
        total_bandwidth += entry.bandwidth_allocation
        total_power += entry.power_consumption
        
        # Check constraints
        return (total_computation <= 1.0 and  # Computation ratio <= 100%
                total_bandwidth <= self.total_bandwidth and
                total_power <= self.max_power)
    
    def find_next_available_slot(self, duration: float, 
                                computation_ratio: float,
                                bandwidth_needed: float,
                                power_needed: float,
                                earliest_start: float = 0.0) -> Optional[float]:
        """
        Find the next available time slot for a task with given requirements
        
        Args:
            duration: Required duration for the task
            computation_ratio: Required computation ratio (0-1)
            bandwidth_needed: Required bandwidth (Mbps)
            power_needed: Required power (Watts)
            earliest_start: Earliest possible start time
            
        Returns:
            Optional[float]: Start time of available slot, or None if not found
        """
        # Check if requirements exceed total capacity
        if (computation_ratio > 1.0 or 
            bandwidth_needed > self.total_bandwidth or
            power_needed > self.max_power):
            return None
        
        # Sort schedule by start time
        sorted_schedule = sorted(self.schedule_queue, key=lambda x: x.start_time)
        
        # Try to fit before the first scheduled entry
        if not sorted_schedule or earliest_start + duration <= sorted_schedule[0].start_time:
            return max(earliest_start, 0.0)
        
        # Try to fit between scheduled entries
        for i in range(len(sorted_schedule) - 1):
            current_end = sorted_schedule[i].end_time
            next_start = sorted_schedule[i + 1].start_time
            
            slot_start = max(current_end, earliest_start)
            slot_end = slot_start + duration
            
            if slot_end <= next_start:
                # Check if resources are available in this slot
                temp_entry = ScheduleEntry(
                    start_time=slot_start,
                    end_time=slot_end,
                    task_id=-1,  # Temporary
                    round_num=-1,
                    computation_ratio=computation_ratio,
                    bandwidth_allocation=bandwidth_needed,
                    power_consumption=power_needed
                )
                
                if self._check_resource_availability(temp_entry):
                    return slot_start
        
        # Try to fit after the last scheduled entry
        if sorted_schedule:
            last_end = sorted_schedule[-1].end_time
            slot_start = max(last_end, earliest_start)
            return slot_start
        
        return earliest_start
    
    def remove_completed_entries(self, current_time: float):
        """Remove completed schedule entries"""
        self.schedule_queue = [
            entry for entry in self.schedule_queue 
            if entry.end_time > current_time
        ]
    
    def get_current_resource_usage(self, current_time: float) -> Dict[str, float]:
        """Get current resource usage at given time"""
        active_entries = [
            entry for entry in self.schedule_queue
            if entry.start_time <= current_time < entry.end_time
        ]
        
        computation_usage = sum(entry.computation_ratio for entry in active_entries)
        bandwidth_usage = sum(entry.bandwidth_allocation for entry in active_entries)
        power_usage = sum(entry.power_consumption for entry in active_entries)
        
        return {
            'computation_ratio': computation_usage,
            'bandwidth_usage': bandwidth_usage,
            'power_usage': power_usage,
            'active_tasks': len(active_entries)
        }
    
    def get_resource_utilization_stats(self, time_window: float = None) -> Dict:
        """Get resource utilization statistics"""
        if not self.schedule_queue:
            return {
                'avg_computation_utilization': 0.0,
                'avg_bandwidth_utilization': 0.0,
                'avg_power_utilization': 0.0,
                'total_scheduled_time': 0.0
            }
        
        # Calculate total scheduled time and resource usage
        total_time = 0.0
        total_computation_time = 0.0
        total_bandwidth_time = 0.0
        total_power_time = 0.0
        
        for entry in self.schedule_queue:
            duration = entry.duration
            total_time += duration
            total_computation_time += entry.computation_ratio * duration
            total_bandwidth_time += entry.bandwidth_allocation * duration
            total_power_time += entry.power_consumption * duration
        
        if total_time == 0:
            return {
                'avg_computation_utilization': 0.0,
                'avg_bandwidth_utilization': 0.0,
                'avg_power_utilization': 0.0,
                'total_scheduled_time': 0.0
            }
        
        return {
            'avg_computation_utilization': total_computation_time / total_time,
            'avg_bandwidth_utilization': total_bandwidth_time / (total_time * self.total_bandwidth),
            'avg_power_utilization': total_power_time / (total_time * self.max_power),
            'total_scheduled_time': total_time,
            'num_scheduled_entries': len(self.schedule_queue)
        }
    
    def get_schedule_summary(self) -> List[Dict]:
        """Get a summary of the current schedule"""
        return [
            {
                'start_time': entry.start_time,
                'end_time': entry.end_time,
                'duration': entry.duration,
                'task_id': entry.task_id,
                'round_num': entry.round_num,
                'computation_ratio': entry.computation_ratio,
                'bandwidth_allocation': entry.bandwidth_allocation,
                'power_consumption': entry.power_consumption
            }
            for entry in sorted(self.schedule_queue, key=lambda x: x.start_time)
        ]
