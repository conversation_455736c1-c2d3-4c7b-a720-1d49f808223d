"""
MADDPG Environment for Multi-Task Federated Learning
"""
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import heapq
from collections import defaultdict

from ..tasks.task_manager import TaskManager, TaskEvent
from ..clients.client import Client
from ..server.fl_server import FLServer
from ..agents.maddpg_agent import MADDPGAgent
from .state_manager import StateManager
from .reward_calculator import RewardCalculator


class MADDPGEnvironment:
    """
    Event-driven simulation environment for MADDPG multi-task FL
    
    Key features:
    - env.step() triggered only by task arrivals and round completions
    - Each client runs a MADDPG agent for task scheduling
    - 5-parameter actions: (Selected_ij, q_ij, B_ij, P_ij, start_time_ij)
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.current_time = 0.0
        self.episode_step = 0
        self.max_episode_length = config['training']['max_episode_length']
        
        # Initialize components
        self.client_ids = list(range(config['clients']['num_clients']))
        self.task_manager = TaskManager(config, self.client_ids)
        self.server = FLServer(config)
        self.state_manager = StateManager(config)
        self.reward_calculator = RewardCalculator(config)
        
        # Initialize clients
        self.clients: Dict[int, Client] = {}
        for client_id in self.client_ids:
            self.clients[client_id] = Client(client_id, config)
        
        # Initialize MADDPG agents
        self.agents: Dict[int, MADDPGAgent] = {}
        state_dim = self.state_manager.state_dim
        action_dim = 5  # 5-parameter actions
        
        for client_id in self.client_ids:
            self.agents[client_id] = MADDPGAgent(
                agent_id=client_id,
                state_dim=state_dim,
                action_dim=action_dim,
                config=config
            )
        
        # Event tracking
        self.pending_events = []  # Events waiting to be processed
        self.completed_rounds = []  # Completed round tracking
        
        # Episode tracking
        self.episode_rewards = defaultdict(list)
        self.episode_stats = {}
        
        # Performance metrics
        self.total_tasks_completed = 0
        self.total_tasks_expired = 0
        self.total_energy_consumed = 0.0
        
    def reset(self) -> Dict[int, np.ndarray]:
        """
        Reset environment for new episode
        
        Returns:
            Dict[int, np.ndarray]: Initial states for all agents
        """
        self.current_time = 0.0
        self.episode_step = 0
        
        # Reset all components
        self.task_manager = TaskManager(self.config, self.client_ids)
        self.server = FLServer(self.config)
        
        # Reset clients
        for client in self.clients.values():
            client.task_queue.clear()
            client.completed_tasks.clear()
            client.resource_manager.schedule_queue.clear()
            client.total_energy_consumed = 0.0
            client.total_tasks_completed = 0
            client.total_rounds_executed = 0
            client.current_time = 0.0
        
        # Reset tracking
        self.pending_events.clear()
        self.completed_rounds.clear()
        self.episode_rewards = defaultdict(list)
        self.episode_stats = {}
        
        # Reset performance metrics
        self.total_tasks_completed = 0
        self.total_tasks_expired = 0
        self.total_energy_consumed = 0.0
        
        # Get initial states
        initial_states = {}
        global_info = self._get_global_info()
        
        for client_id, client in self.clients.items():
            state = self.state_manager.get_client_state(client, self.current_time, global_info)
            initial_states[client_id] = state
        
        return initial_states
    
    def step(self) -> Tuple[Dict[int, np.ndarray], Dict[int, float], Dict[int, bool], Dict]:
        """
        Execute one environment step (triggered by events)

        Returns:
            Tuple: (next_states, rewards, dones, info)
        """
        self.episode_step += 1

        # Get next event
        event = self.task_manager.get_next_event()
        if event is None:
            # No more events - episode ends
            return self._end_episode()

        # Update current time
        self.current_time = event.time
        self.task_manager.update_time(self.current_time)

        # Update all clients' time
        for client in self.clients.values():
            client.update_time(self.current_time)

        # Process event and get rewards
        rewards = self._process_event(event)

        # Get next states
        next_states = {}
        global_info = self._get_global_info()

        for client_id, client in self.clients.items():
            state = self.state_manager.get_client_state(client, self.current_time, global_info)
            next_states[client_id] = state

        # Check if episode should end
        dones = self._check_episode_end()

        # Collect info
        info = self._get_step_info(event)

        return next_states, rewards, dones, info

    def run_episode(self) -> Dict[str, Any]:
        """
        Run a complete episode using event-driven simulation

        This method implements the true event-driven approach where env.step()
        is called only when events occur, not in a continuous loop.

        Returns:
            Dict: Episode results and statistics
        """
        # Reset environment
        states = self.reset()
        episode_done = False
        episode_results = {
            'states_history': [states],
            'rewards_history': [],
            'events_processed': [],
            'total_steps': 0
        }

        print(f"Episode started - {len(self.task_manager.event_queue)} events scheduled")

        # Event-driven loop - step() called only when events occur
        while not episode_done:
            # Check if there are any pending events
            if len(self.task_manager.event_queue) == 0:
                print("No more events - episode ending")
                break

            # Process next event by calling step()
            next_states, rewards, dones, info = self.step()

            # Store episode data
            episode_results['states_history'].append(next_states)
            episode_results['rewards_history'].append(rewards)
            episode_results['events_processed'].append(info)
            episode_results['total_steps'] += 1

            # Print event information
            event_type = info.get('event_type', 'unknown')
            task_id = info.get('task_id', 'N/A')
            client_id = info.get('client_id', 'N/A')
            current_time = info.get('current_time', 0)

            print(f"Step {episode_results['total_steps']}: t={current_time:.1f} - "
                  f"{event_type} (Task {task_id}, Client {client_id})")

            # Update states for next iteration
            states = next_states
            episode_done = all(dones.values())

            # Safety check to prevent infinite loops
            if episode_results['total_steps'] >= self.max_episode_length:
                print(f"Episode terminated - reached max length {self.max_episode_length}")
                break

        # Get final episode statistics
        final_stats = self.get_episode_statistics()
        episode_results['final_stats'] = final_stats

        print(f"Episode completed: {episode_results['total_steps']} events processed")
        print(f"Tasks completed: {final_stats.get('total_tasks_completed', 0)}")
        print(f"Tasks expired: {final_stats.get('total_tasks_expired', 0)}")

        return episode_results

    def _process_event(self, event: TaskEvent) -> Dict[int, float]:
        """
        Process a task event and return rewards

        Args:
            event: TaskEvent to process

        Returns:
            Dict[int, float]: Rewards for each client
        """
        rewards = {client_id: 0.0 for client_id in self.client_ids}

        if event.event_type == 'arrival':
            rewards = self._handle_task_arrival(event)
        elif event.event_type == 'round_complete':
            rewards = self._handle_round_completion(event)
        elif event.event_type == 'deadline':
            rewards = self._handle_deadline_expiry(event)

        # Store rewards for episode tracking
        for client_id, reward in rewards.items():
            self.episode_rewards[client_id].append(reward)

        return rewards

    def _handle_task_arrival(self, event: TaskEvent) -> Dict[int, float]:
        """Handle task arrival event"""
        # Create new task
        task = self.task_manager.handle_task_arrival(event)

        # Assign task to client
        client = self.clients[event.client_id]
        client.add_task(task)

        # Register task with server
        self.server.register_task(task)

        # Get agent action for the new task
        agent = self.agents[event.client_id]
        state = self.state_manager.get_client_state(client, self.current_time, self._get_global_info())

        task_context = {
            'current_time': self.current_time,
            'deadline': task.deadline,
            'task_id': task.task_id
        }

        action = agent.get_action(state, task_context, add_noise=True)

        # Normalize action
        action = self.state_manager.normalize_action(action, client)

        # Calculate reward for task arrival decision
        arrival_reward = self.reward_calculator.calculate_task_arrival_reward(
            client, task, action, self.current_time
        )

        # Schedule task if selected
        if action.get('selected', False):
            success, schedule_entry = client.schedule_task_round(task.task_id, action)

            if success:
                # Schedule round completion event
                completion_time = schedule_entry.end_time
                completion_event = TaskEvent(
                    'round_complete', completion_time, task.task_id, event.client_id,
                    schedule_entry=schedule_entry
                )
                heapq.heappush(self.task_manager.event_queue, completion_event)
            else:
                # Penalty for failed scheduling
                arrival_reward -= 5.0

        # Return rewards (only the affected client gets reward)
        rewards = {client_id: 0.0 for client_id in self.client_ids}
        rewards[event.client_id] = arrival_reward

        return rewards

    def _handle_round_completion(self, event: TaskEvent) -> Dict[int, float]:
        """Handle round completion event"""
        client = self.clients[event.client_id]
        task = client.get_task_by_id(event.task_id)

        if task is None:
            return {client_id: 0.0 for client_id in self.client_ids}

        # Get schedule entry from event
        schedule_entry = event.kwargs.get('schedule_entry')
        if schedule_entry is None:
            return {client_id: 0.0 for client_id in self.client_ids}

        # Execute the round
        execution_result = client.execute_scheduled_round(schedule_entry)

        if not execution_result['success']:
            return {client_id: 0.0 for client_id in self.client_ids}

        # Update server with model
        new_accuracy = execution_result['new_accuracy']
        self.server.receive_model_update(
            event.task_id, event.client_id, schedule_entry.round_num,
            model_weights={}, local_accuracy=new_accuracy,
            data_size=task.data_sizes.get(event.client_id, 1000)
        )

        # Aggregate round if ready
        success, global_accuracy = self.server.aggregate_round(
            event.task_id, schedule_entry.round_num, self.current_time
        )

        if success:
            # Update task accuracy
            task.update_accuracy(global_accuracy, self.current_time)

            # Check if task is completed
            if task.is_completed:
                client.remove_task(event.task_id)
                self.total_tasks_completed += 1
            else:
                # Schedule next round decision
                # Agent will decide on next round in next step
                pass

        # Calculate reward
        round_reward = self.reward_calculator.calculate_round_reward(
            client, task, execution_result, self.current_time
        )

        # Update energy tracking
        energy_consumed = execution_result.get('energy_consumed', 0.0)
        self.total_energy_consumed += energy_consumed

        # Return rewards
        rewards = {client_id: 0.0 for client_id in self.client_ids}
        rewards[event.client_id] = round_reward

        return rewards

    def _handle_deadline_expiry(self, event: TaskEvent) -> Dict[int, float]:
        """Handle task deadline expiry"""
        self.task_manager.handle_deadline_expiry(event)

        # Find which client had this task
        affected_client_id = None
        for client_id, client in self.clients.items():
            if client.get_task_by_id(event.task_id) is not None:
                affected_client_id = client_id
                break

        if affected_client_id is not None:
            client = self.clients[affected_client_id]
            task = client.get_task_by_id(event.task_id)

            if task is not None:
                # Calculate deadline violation penalty
                penalty = self.reward_calculator.calculate_deadline_violation_penalty(
                    task, self.current_time
                )

                # Remove expired task
                client.remove_task(event.task_id)
                self.total_tasks_expired += 1

                # Return penalty
                rewards = {client_id: 0.0 for client_id in self.client_ids}
                rewards[affected_client_id] = penalty

                return rewards

        return {client_id: 0.0 for client_id in self.client_ids}

    def _get_global_info(self) -> Dict:
        """Get global system information"""
        # Calculate system-wide metrics
        all_tasks = []
        for client in self.clients.values():
            all_tasks.extend(client.task_queue)

        total_active_tasks = len(all_tasks)

        # Calculate completion and violation rates
        total_tasks = self.total_tasks_completed + self.total_tasks_expired + total_active_tasks
        completion_rate = self.total_tasks_completed / max(1, total_tasks)
        violation_rate = self.total_tasks_expired / max(1, total_tasks)

        # Calculate system load
        total_computation_usage = sum(
            client.resource_manager.get_current_resource_usage(self.current_time)['computation_ratio']
            for client in self.clients.values()
        )
        system_load = total_computation_usage / len(self.clients)

        return {
            'current_time': self.current_time,
            'total_active_tasks': total_active_tasks,
            'system_load': system_load,
            'avg_task_completion_rate': completion_rate,
            'avg_deadline_violation_rate': violation_rate
        }

    def _check_episode_end(self) -> Dict[int, bool]:
        """Check if episode should end"""
        # Episode ends if max length reached or no more events
        episode_done = (
            self.episode_step >= self.max_episode_length or
            len(self.task_manager.event_queue) == 0
        )

        return {client_id: episode_done for client_id in self.client_ids}

    def _end_episode(self) -> Tuple[Dict[int, np.ndarray], Dict[int, float], Dict[int, bool], Dict]:
        """Handle episode end"""
        # Get final states
        final_states = {}
        global_info = self._get_global_info()

        for client_id, client in self.clients.items():
            state = self.state_manager.get_client_state(client, self.current_time, global_info)
            final_states[client_id] = state

        # Calculate final rewards (global performance)
        all_tasks = []
        for client in self.clients.values():
            all_tasks.extend(client.task_queue)

        global_rewards = self.reward_calculator.calculate_global_performance_reward(
            list(self.clients.values()), all_tasks
        )

        # All agents are done
        dones = {client_id: True for client_id in self.client_ids}

        # Final info
        info = {
            'episode_end': True,
            'total_tasks_completed': self.total_tasks_completed,
            'total_tasks_expired': self.total_tasks_expired,
            'total_energy_consumed': self.total_energy_consumed,
            'episode_length': self.episode_step
        }

        return final_states, global_rewards, dones, info

    def _get_step_info(self, event: TaskEvent) -> Dict:
        """Get step information"""
        return {
            'event_type': event.event_type,
            'event_time': event.time,
            'task_id': event.task_id,
            'client_id': event.client_id,
            'current_time': self.current_time,
            'episode_step': self.episode_step,
            'active_tasks': sum(len(client.task_queue) for client in self.clients.values()),
            'total_energy': self.total_energy_consumed
        }

    def get_agent_action_for_task(self, client_id: int, task_id: int) -> Dict:
        """
        Get agent action for a specific task

        Args:
            client_id: ID of the client
            task_id: ID of the task

        Returns:
            Dict: Agent action
        """
        client = self.clients[client_id]
        agent = self.agents[client_id]
        task = client.get_task_by_id(task_id)

        if task is None:
            return {'selected': False}

        # Get current state
        state = self.state_manager.get_client_state(client, self.current_time, self._get_global_info())

        # Task context
        task_context = {
            'current_time': self.current_time,
            'deadline': task.deadline,
            'task_id': task.task_id
        }

        # Get action
        action = agent.get_action(state, task_context, add_noise=True)

        # Normalize action
        action = self.state_manager.normalize_action(action, client)

        return action

    def train_agents(self) -> Dict[int, Dict[str, float]]:
        """
        Train all MADDPG agents

        Returns:
            Dict[int, Dict[str, float]]: Training losses for each agent
        """
        training_results = {}

        for client_id, agent in self.agents.items():
            # Train individual agent
            losses = agent.update()
            training_results[client_id] = losses

        return training_results

    def get_episode_statistics(self) -> Dict:
        """Get statistics for the current episode"""
        # Calculate average rewards
        avg_rewards = {}
        for client_id, rewards in self.episode_rewards.items():
            avg_rewards[client_id] = np.mean(rewards) if rewards else 0.0

        # Task statistics
        task_stats = self.task_manager.get_statistics()

        # Client statistics
        client_stats = {}
        for client_id, client in self.clients.items():
            client_stats[client_id] = client.get_performance_metrics()

        # Agent statistics
        agent_stats = {}
        for client_id, agent in self.agents.items():
            agent_stats[client_id] = agent.get_statistics()

        return {
            'episode_step': self.episode_step,
            'current_time': self.current_time,
            'avg_rewards': avg_rewards,
            'total_rewards': {cid: sum(rewards) for cid, rewards in self.episode_rewards.items()},
            'task_stats': task_stats,
            'client_stats': client_stats,
            'agent_stats': agent_stats,
            'total_tasks_completed': self.total_tasks_completed,
            'total_tasks_expired': self.total_tasks_expired,
            'total_energy_consumed': self.total_energy_consumed
        }

    def save_agents(self, filepath_prefix: str):
        """Save all agent models"""
        for client_id, agent in self.agents.items():
            filepath = f"{filepath_prefix}_agent_{client_id}.pth"
            agent.save_model(filepath)

    def load_agents(self, filepath_prefix: str):
        """Load all agent models"""
        for client_id, agent in self.agents.items():
            filepath = f"{filepath_prefix}_agent_{client_id}.pth"
            try:
                agent.load_model(filepath)
            except FileNotFoundError:
                print(f"Warning: Could not load model for agent {client_id}")

    def render(self, mode='human') -> Optional[str]:
        """Render environment state"""
        if mode == 'human':
            print(f"\n=== Time: {self.current_time:.2f} ===")
            print(f"Episode Step: {self.episode_step}")
            print(f"Active Tasks: {sum(len(client.task_queue) for client in self.clients.values())}")
            print(f"Completed Tasks: {self.total_tasks_completed}")
            print(f"Expired Tasks: {self.total_tasks_expired}")
            print(f"Total Energy: {self.total_energy_consumed:.2f}")

            for client_id, client in self.clients.items():
                print(f"\nClient {client_id}:")
                print(f"  Tasks: {len(client.task_queue)}")
                print(f"  Scheduled: {len(client.resource_manager.schedule_queue)}")
                resource_usage = client.resource_manager.get_current_resource_usage(self.current_time)
                print(f"  CPU: {resource_usage['computation_ratio']:.2f}")
                print(f"  BW: {resource_usage['bandwidth_usage']:.1f}")
                print(f"  Power: {resource_usage['power_usage']:.1f}")

        return None
