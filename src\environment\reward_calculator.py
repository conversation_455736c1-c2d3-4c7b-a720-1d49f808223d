"""
Reward Calculation for MADDPG FL Environment
"""
import numpy as np
from typing import Dict, List, Optional, Tuple

from ..tasks.fl_task import FLTask
from ..clients.client import Client


class RewardCalculator:
    """
    Calculates rewards for MADDPG agents based on FL performance
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Reward weights
        self.accuracy_weight = config['rewards']['accuracy_weight']
        self.energy_weight = config['rewards']['energy_weight']
        self.deadline_weight = config['rewards']['deadline_weight']
        self.completion_bonus = config['rewards']['completion_bonus']
        
        # Normalization factors
        self.max_energy_per_round = 100.0  # Maximum expected energy per round
        self.max_deadline_penalty = 50.0   # Maximum deadline penalty
    
    def calculate_round_reward(self, client: Client, task: FLTask, 
                             execution_result: Dict, current_time: float) -> float:
        """
        Calculate reward for completing a task round
        
        Args:
            client: Client that executed the round
            task: FL task
            execution_result: Results from round execution
            current_time: Current simulation time
            
        Returns:
            float: Calculated reward
        """
        reward = 0.0
        
        # 1. Accuracy improvement reward
        accuracy_improvement = execution_result.get('improvement', 0.0)
        accuracy_reward = self.accuracy_weight * accuracy_improvement * 100  # Scale to reasonable range
        reward += accuracy_reward
        
        # 2. Energy efficiency penalty
        energy_consumed = execution_result.get('energy_consumed', 0.0)
        energy_penalty = self.energy_weight * (energy_consumed / self.max_energy_per_round)
        reward -= energy_penalty
        
        # 3. Deadline urgency consideration
        remaining_time = task.remaining_time(current_time)
        if remaining_time <= 0:
            # Task is overdue - heavy penalty
            deadline_penalty = self.deadline_weight * self.max_deadline_penalty
            reward -= deadline_penalty
        else:
            # Reward for working on urgent tasks
            urgency_score = task.get_urgency_score(current_time)
            urgency_reward = self.deadline_weight * min(10.0, urgency_score)
            reward += urgency_reward
        
        # 4. Task completion bonus
        if task.is_completed:
            completion_reward = self.completion_bonus
            # Bonus scales with how early the task was completed
            if remaining_time > 0:
                early_completion_bonus = self.completion_bonus * 0.5 * (remaining_time / (task.deadline - task.arrival_time))
                completion_reward += early_completion_bonus
            reward += completion_reward
        
        return reward
    
    def calculate_task_arrival_reward(self, client: Client, task: FLTask, 
                                    action: Dict, current_time: float) -> float:
        """
        Calculate reward for task arrival decision
        
        Args:
            client: Client receiving the task
            task: New FL task
            action: Agent's action for the task
            current_time: Current simulation time
            
        Returns:
            float: Calculated reward
        """
        reward = 0.0
        
        # If agent chooses not to participate, small penalty for missed opportunity
        if not action.get('selected', False):
            reward -= 1.0
            return reward
        
        # Reward for accepting urgent tasks
        urgency_score = task.get_urgency_score(current_time)
        urgency_reward = min(5.0, urgency_score)
        reward += urgency_reward
        
        # Penalty for resource over-allocation
        computation_ratio = action.get('computation_ratio', 0.0)
        bandwidth = action.get('bandwidth', 0.0)
        power = action.get('power', 0.0)
        
        # Check current resource usage
        resource_usage = client.resource_manager.get_current_resource_usage(current_time)
        
        # Penalty for exceeding capacity
        total_computation = resource_usage['computation_ratio'] + computation_ratio
        total_bandwidth = resource_usage['bandwidth_usage'] + bandwidth
        total_power = resource_usage['power_usage'] + power
        
        if total_computation > 1.0:
            reward -= 10.0 * (total_computation - 1.0)
        
        if total_bandwidth > client.total_bandwidth:
            reward -= 5.0 * ((total_bandwidth - client.total_bandwidth) / client.total_bandwidth)
        
        if total_power > client.max_power:
            reward -= 5.0 * ((total_power - client.max_power) / client.max_power)
        
        return reward
    
    def calculate_deadline_violation_penalty(self, task: FLTask, current_time: float) -> float:
        """
        Calculate penalty for deadline violation
        
        Args:
            task: FL task that violated deadline
            current_time: Current simulation time
            
        Returns:
            float: Penalty (negative reward)
        """
        if current_time <= task.deadline:
            return 0.0
        
        # Penalty increases with how much the deadline was exceeded
        overdue_time = current_time - task.deadline
        total_time = task.deadline - task.arrival_time
        
        # Penalty scales with task progress (less penalty if task was nearly complete)
        progress_factor = 1.0 - task.get_progress_ratio()
        
        penalty = self.deadline_weight * self.max_deadline_penalty * progress_factor
        penalty *= min(2.0, overdue_time / total_time)  # Cap at 2x penalty
        
        return -penalty
    
    def calculate_resource_efficiency_reward(self, client: Client, 
                                           time_window: float = 100.0) -> float:
        """
        Calculate reward for efficient resource utilization
        
        Args:
            client: Client to evaluate
            time_window: Time window for evaluation
            
        Returns:
            float: Efficiency reward
        """
        stats = client.resource_manager.get_resource_utilization_stats()
        
        # Reward balanced resource utilization
        computation_util = stats.get('avg_computation_utilization', 0.0)
        bandwidth_util = stats.get('avg_bandwidth_utilization', 0.0)
        power_util = stats.get('avg_power_utilization', 0.0)
        
        # Optimal utilization is around 70-80%
        optimal_range = (0.7, 0.8)
        
        def utilization_reward(util):
            if optimal_range[0] <= util <= optimal_range[1]:
                return 5.0  # Good utilization
            elif util < optimal_range[0]:
                return 2.0 * util / optimal_range[0]  # Under-utilization
            else:
                return 5.0 * (1.0 - util) / (1.0 - optimal_range[1])  # Over-utilization
        
        computation_reward = utilization_reward(computation_util)
        bandwidth_reward = utilization_reward(bandwidth_util)
        power_reward = utilization_reward(power_util)
        
        return (computation_reward + bandwidth_reward + power_reward) / 3.0
    
    def calculate_global_performance_reward(self, all_clients: List[Client], 
                                          all_tasks: List[FLTask]) -> Dict[int, float]:
        """
        Calculate global performance rewards for all clients
        
        Args:
            all_clients: List of all clients
            all_tasks: List of all tasks
            
        Returns:
            Dict[int, float]: Client ID -> global reward
        """
        rewards = {}
        
        if not all_tasks:
            return {client.client_id: 0.0 for client in all_clients}
        
        # Calculate system-wide metrics
        total_completed = sum(1 for task in all_tasks if task.is_completed)
        total_expired = sum(1 for task in all_tasks if task.is_expired)
        
        completion_rate = total_completed / len(all_tasks)
        expiry_rate = total_expired / len(all_tasks)
        
        # Global reward based on system performance
        global_reward = 10.0 * completion_rate - 15.0 * expiry_rate
        
        # Distribute global reward based on individual contributions
        total_contributions = sum(client.total_rounds_executed for client in all_clients)
        
        for client in all_clients:
            if total_contributions > 0:
                contribution_ratio = client.total_rounds_executed / total_contributions
                rewards[client.client_id] = global_reward * contribution_ratio
            else:
                rewards[client.client_id] = 0.0
        
        return rewards
    
    def get_reward_breakdown(self, reward_components: Dict) -> str:
        """
        Get a human-readable breakdown of reward components
        
        Args:
            reward_components: Dictionary of reward components
            
        Returns:
            str: Formatted reward breakdown
        """
        breakdown = "Reward Breakdown:\n"
        total = 0.0
        
        for component, value in reward_components.items():
            breakdown += f"  {component}: {value:.3f}\n"
            total += value
        
        breakdown += f"  Total: {total:.3f}"
        return breakdown
