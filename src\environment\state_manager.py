"""
State Management for MADDPG FL Environment
"""
import numpy as np
from typing import Dict, List, Optional, Tuple, Any

from ..tasks.fl_task import FLTask
from ..clients.client import Client
from ..server.fl_server import FLServer


class StateManager:
    """
    Manages state representation for MADDPG agents
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # State dimensions
        self.max_tasks_per_client = config.get('max_tasks_per_client', 10)
        self.max_schedule_entries = config.get('max_schedule_entries', 20)
        
        # Feature dimensions
        self.task_feature_dim = 10  # Features per task
        self.resource_feature_dim = 8  # Resource-related features
        self.schedule_feature_dim = 6  # Features per schedule entry
        self.global_feature_dim = 5  # Global system features
        
        # Calculate total state dimension
        self.state_dim = (
            self.max_tasks_per_client * self.task_feature_dim +
            self.resource_feature_dim +
            self.max_schedule_entries * self.schedule_feature_dim +
            self.global_feature_dim
        )
    
    def get_client_state(self, client: Client, current_time: float,
                        global_info: Dict = None) -> np.ndarray:
        """
        Get state observation for a specific client
        
        Args:
            client: Client to get state for
            current_time: Current simulation time
            global_info: Global system information
            
        Returns:
            np.ndarray: State vector
        """
        state_components = []
        
        # 1. Task queue features
        task_features = self._get_task_features(client.task_queue, current_time)
        state_components.append(task_features)
        
        # 2. Resource utilization features
        resource_features = self._get_resource_features(client, current_time)
        state_components.append(resource_features)
        
        # 3. Schedule queue features
        schedule_features = self._get_schedule_features(client.resource_manager, current_time)
        state_components.append(schedule_features)
        
        # 4. Global system features
        global_features = self._get_global_features(global_info or {})
        state_components.append(global_features)
        
        # Concatenate all features
        state = np.concatenate(state_components)
        
        return state
    
    def _get_task_features(self, tasks: List[FLTask], current_time: float) -> np.ndarray:
        """Extract features from task queue"""
        features = np.zeros(self.max_tasks_per_client * self.task_feature_dim)
        
        for i, task in enumerate(tasks[:self.max_tasks_per_client]):
            base_idx = i * self.task_feature_dim
            
            # Normalize features to [0, 1] range
            remaining_time = max(0, task.remaining_time(current_time))
            total_time = task.deadline - task.arrival_time
            
            task_features = [
                task.task_id / 1000.0,  # Normalized task ID
                remaining_time / max(1, total_time),  # Remaining time ratio
                task.target_accuracy,  # Target accuracy [0, 1]
                task.current_accuracy,  # Current accuracy [0, 1]
                task.current_round / max(1, task.max_global_rounds),  # Round progress
                task.get_progress_ratio(),  # Overall progress
                min(1.0, task.get_urgency_score(current_time) / 10.0),  # Urgency score
                task.model_size / 100.0,  # Normalized model size
                len(task.data_sizes) / 10.0,  # Number of participating clients
                task.total_energy_consumed / 1000.0  # Normalized energy consumed
            ]
            
            features[base_idx:base_idx + self.task_feature_dim] = task_features
        
        return features
    
    def _get_resource_features(self, client: Client, current_time: float) -> np.ndarray:
        """Extract resource utilization features"""
        resource_usage = client.resource_manager.get_current_resource_usage(current_time)
        resource_stats = client.resource_manager.get_resource_utilization_stats()
        
        features = [
            resource_usage['computation_ratio'],  # Current computation usage [0, 1]
            resource_usage['bandwidth_usage'] / client.total_bandwidth,  # Current bandwidth usage [0, 1]
            resource_usage['power_usage'] / client.max_power,  # Current power usage [0, 1]
            resource_usage['active_tasks'] / 10.0,  # Number of active tasks
            resource_stats.get('avg_computation_utilization', 0.0),  # Historical computation util
            resource_stats.get('avg_bandwidth_utilization', 0.0),  # Historical bandwidth util
            resource_stats.get('avg_power_utilization', 0.0),  # Historical power util
            client.total_energy_consumed / 10000.0  # Total energy consumed (normalized)
        ]
        
        return np.array(features)
    
    def _get_schedule_features(self, resource_manager, current_time: float) -> np.ndarray:
        """Extract schedule queue features"""
        features = np.zeros(self.max_schedule_entries * self.schedule_feature_dim)
        
        # Get upcoming schedule entries
        upcoming_entries = [
            entry for entry in resource_manager.schedule_queue
            if entry.start_time >= current_time
        ]
        
        for i, entry in enumerate(upcoming_entries[:self.max_schedule_entries]):
            base_idx = i * self.schedule_feature_dim
            
            schedule_features = [
                (entry.start_time - current_time) / 1000.0,  # Time until start (normalized)
                entry.duration / 100.0,  # Duration (normalized)
                entry.computation_ratio,  # Computation ratio [0, 1]
                entry.bandwidth_allocation / 50.0,  # Bandwidth (normalized)
                entry.power_consumption / 10.0,  # Power (normalized)
                entry.task_id / 1000.0  # Task ID (normalized)
            ]
            
            features[base_idx:base_idx + self.schedule_feature_dim] = schedule_features
        
        return features
    
    def _get_global_features(self, global_info: Dict) -> np.ndarray:
        """Extract global system features"""
        features = [
            global_info.get('system_load', 0.0),  # Overall system load [0, 1]
            global_info.get('avg_task_completion_rate', 0.0),  # Task completion rate [0, 1]
            global_info.get('avg_deadline_violation_rate', 0.0),  # Deadline violation rate [0, 1]
            global_info.get('total_active_tasks', 0) / 100.0,  # Total active tasks (normalized)
            global_info.get('current_time', 0.0) / 10000.0  # Current time (normalized)
        ]
        
        return np.array(features)
    
    def get_action_mask(self, client: Client, task: FLTask, current_time: float) -> np.ndarray:
        """
        Get action mask for valid actions
        
        Args:
            client: Client making the decision
            task: Task to make decision for
            current_time: Current simulation time
            
        Returns:
            np.ndarray: Binary mask for valid actions
        """
        # For now, all actions are valid (no hard constraints)
        # In a more complex implementation, this could mask invalid resource allocations
        return np.ones(5)  # 5 action components
    
    def normalize_action(self, action: Dict, client: Client) -> Dict:
        """
        Normalize action values to valid ranges
        
        Args:
            action: Raw action from agent
            client: Client executing the action
            
        Returns:
            Dict: Normalized action
        """
        normalized_action = action.copy()
        
        # Ensure computation ratio is in [0, 1]
        normalized_action['computation_ratio'] = np.clip(
            action.get('computation_ratio', 0.0), 0.0, 1.0
        )
        
        # Ensure bandwidth is in [0, total_bandwidth]
        normalized_action['bandwidth'] = np.clip(
            action.get('bandwidth', 0.0), 0.0, client.total_bandwidth
        )
        
        # Ensure power is in [0, max_power]
        normalized_action['power'] = np.clip(
            action.get('power', 0.0), 0.0, client.max_power
        )
        
        # Ensure timing offset is in [-1, 1]
        normalized_action['timing_offset'] = np.clip(
            action.get('timing_offset', 0.0), -1.0, 1.0
        )
        
        return normalized_action
    
    def get_state_info(self) -> Dict:
        """Get information about state representation"""
        return {
            'state_dim': self.state_dim,
            'task_feature_dim': self.task_feature_dim,
            'resource_feature_dim': self.resource_feature_dim,
            'schedule_feature_dim': self.schedule_feature_dim,
            'global_feature_dim': self.global_feature_dim,
            'max_tasks_per_client': self.max_tasks_per_client,
            'max_schedule_entries': self.max_schedule_entries
        }
    
    def decode_state(self, state: np.ndarray) -> Dict:
        """
        Decode state vector into interpretable components
        
        Args:
            state: State vector
            
        Returns:
            Dict: Decoded state components
        """
        idx = 0
        
        # Task features
        task_features = state[idx:idx + self.max_tasks_per_client * self.task_feature_dim]
        idx += self.max_tasks_per_client * self.task_feature_dim
        
        # Resource features
        resource_features = state[idx:idx + self.resource_feature_dim]
        idx += self.resource_feature_dim
        
        # Schedule features
        schedule_features = state[idx:idx + self.max_schedule_entries * self.schedule_feature_dim]
        idx += self.max_schedule_entries * self.schedule_feature_dim
        
        # Global features
        global_features = state[idx:idx + self.global_feature_dim]
        
        return {
            'task_features': task_features.reshape(self.max_tasks_per_client, self.task_feature_dim),
            'resource_features': resource_features,
            'schedule_features': schedule_features.reshape(self.max_schedule_entries, self.schedule_feature_dim),
            'global_features': global_features
        }
