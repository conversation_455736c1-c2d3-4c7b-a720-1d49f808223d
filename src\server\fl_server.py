"""
Federated Learning Server Implementation
"""
import numpy as np
from typing import Dict, List, Optional, Tuple
from collections import defaultdict

from ..tasks.fl_task import FLTask


class FLServer:
    """
    Central Federated Learning Server
    
    Handles model aggregation and coordinates FL training across clients
    """
    
    def __init__(self, config: Dict):
        self.config = config
        
        # Model aggregation tracking
        self.global_models: Dict[int, Dict] = {}  # task_id -> model_state
        self.round_participants: Dict[int, Dict[int, List[int]]] = defaultdict(lambda: defaultdict(list))  # task_id -> round -> client_ids
        self.round_accuracies: Dict[int, List[float]] = defaultdict(list)  # task_id -> accuracies
        
        # Performance tracking
        self.total_rounds_aggregated = 0
        self.total_models_received = 0
        
        # Aggregation parameters
        self.min_participants = config.get('min_participants', 1)
        self.aggregation_method = config.get('aggregation_method', 'fedavg')
    
    def register_task(self, task: FLTask):
        """Register a new FL task with the server"""
        self.global_models[task.task_id] = {
            'weights': None,  # In real implementation, this would be actual model weights
            'version': 0,
            'last_updated': 0.0
        }
        self.round_accuracies[task.task_id] = [0.0]  # Initial accuracy
    
    def receive_model_update(self, task_id: int, client_id: int, round_num: int,
                           model_weights: Dict = None, local_accuracy: float = 0.0,
                           data_size: int = 1000) -> bool:
        """
        Receive model update from a client
        
        Args:
            task_id: ID of the FL task
            client_id: ID of the client
            round_num: Round number
            model_weights: Local model weights (simplified as dict)
            local_accuracy: Local accuracy achieved
            data_size: Size of local dataset
            
        Returns:
            bool: True if update was accepted
        """
        if task_id not in self.global_models:
            return False
        
        # Record participant
        self.round_participants[task_id][round_num].append(client_id)
        self.total_models_received += 1
        
        # In a real implementation, we would store the actual model weights
        # For simulation, we just track participation
        
        return True
    
    def aggregate_round(self, task_id: int, round_num: int, current_time: float) -> Tuple[bool, float]:
        """
        Aggregate models for a specific task round
        
        Args:
            task_id: ID of the FL task
            round_num: Round number to aggregate
            current_time: Current simulation time
            
        Returns:
            Tuple[bool, float]: (success, new_global_accuracy)
        """
        if task_id not in self.global_models:
            return False, 0.0
        
        participants = self.round_participants[task_id][round_num]
        
        if len(participants) < self.min_participants:
            return False, 0.0
        
        # Simulate model aggregation
        # In reality, this would involve actual model weight averaging
        
        # Calculate new global accuracy based on participation
        current_accuracy = self.round_accuracies[task_id][-1] if self.round_accuracies[task_id] else 0.0
        
        # Accuracy improvement based on number of participants and round
        base_improvement = 0.02  # Base improvement per round
        participation_factor = min(1.0, len(participants) / len(self.config.get('client_ids', [1])))
        
        # Diminishing returns as accuracy increases
        diminishing_factor = max(0.1, 1.0 - current_accuracy)
        
        improvement = base_improvement * participation_factor * diminishing_factor
        new_accuracy = min(1.0, current_accuracy + improvement)
        
        # Update global model
        self.global_models[task_id]['version'] = round_num
        self.global_models[task_id]['last_updated'] = current_time
        self.round_accuracies[task_id].append(new_accuracy)
        
        self.total_rounds_aggregated += 1
        
        return True, new_accuracy
    
    def get_global_model(self, task_id: int) -> Optional[Dict]:
        """Get current global model for a task"""
        return self.global_models.get(task_id)
    
    def get_task_accuracy(self, task_id: int) -> float:
        """Get current global accuracy for a task"""
        if task_id in self.round_accuracies and self.round_accuracies[task_id]:
            return self.round_accuracies[task_id][-1]
        return 0.0
    
    def get_round_participants(self, task_id: int, round_num: int) -> List[int]:
        """Get participants for a specific round"""
        return self.round_participants[task_id][round_num]
    
    def is_round_ready_for_aggregation(self, task_id: int, round_num: int) -> bool:
        """Check if a round has enough participants for aggregation"""
        participants = self.round_participants[task_id][round_num]
        return len(participants) >= self.min_participants
    
    def get_task_statistics(self, task_id: int) -> Dict:
        """Get statistics for a specific task"""
        if task_id not in self.global_models:
            return {}
        
        total_participants = sum(
            len(participants) for participants in self.round_participants[task_id].values()
        )
        
        rounds_completed = len(self.round_accuracies[task_id]) - 1  # Subtract initial accuracy
        
        return {
            'task_id': task_id,
            'current_accuracy': self.get_task_accuracy(task_id),
            'rounds_completed': rounds_completed,
            'total_participants': total_participants,
            'avg_participants_per_round': total_participants / max(1, rounds_completed),
            'model_version': self.global_models[task_id]['version'],
            'last_updated': self.global_models[task_id]['last_updated'],
            'accuracy_history': self.round_accuracies[task_id].copy()
        }
    
    def get_server_statistics(self) -> Dict:
        """Get overall server statistics"""
        active_tasks = len(self.global_models)
        total_participants = sum(
            sum(len(participants) for participants in task_rounds.values())
            for task_rounds in self.round_participants.values()
        )
        
        return {
            'active_tasks': active_tasks,
            'total_rounds_aggregated': self.total_rounds_aggregated,
            'total_models_received': self.total_models_received,
            'total_participants': total_participants,
            'avg_participants_per_round': total_participants / max(1, self.total_rounds_aggregated),
            'tasks': list(self.global_models.keys())
        }
    
    def cleanup_old_data(self, keep_recent_rounds: int = 50):
        """Clean up old round data to save memory"""
        for task_id in self.round_participants:
            rounds = list(self.round_participants[task_id].keys())
            if len(rounds) > keep_recent_rounds:
                old_rounds = rounds[:-keep_recent_rounds]
                for round_num in old_rounds:
                    del self.round_participants[task_id][round_num]
        
        # Keep recent accuracy history
        for task_id in self.round_accuracies:
            if len(self.round_accuracies[task_id]) > keep_recent_rounds:
                self.round_accuracies[task_id] = self.round_accuracies[task_id][-keep_recent_rounds:]
