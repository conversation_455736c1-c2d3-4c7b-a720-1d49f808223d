"""
Federated Learning Task Implementation
"""
import numpy as np
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum


class TaskStatus(Enum):
    """Task status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"


@dataclass
class FLTask:
    """
    Federated Learning Task
    
    Represents a single FL task with all its properties and current state.
    """
    task_id: int
    arrival_time: float
    deadline: float
    target_accuracy: float
    data_sizes: Dict[int, int]  # client_id -> data_size mapping
    model_size: float  # Model size in MB
    max_global_rounds: int
    
    # Current state
    current_round: int = 0
    current_accuracy: float = 0.0
    status: TaskStatus = TaskStatus.PENDING
    assigned_clients: List[int] = None
    
    # Performance tracking
    total_energy_consumed: float = 0.0
    total_time_elapsed: float = 0.0
    round_history: List[Dict] = None
    
    def __post_init__(self):
        if self.assigned_clients is None:
            self.assigned_clients = []
        if self.round_history is None:
            self.round_history = []
    
    @property
    def is_completed(self) -> bool:
        """Check if task is completed (reached target accuracy or max rounds)"""
        return (self.current_accuracy >= self.target_accuracy or 
                self.current_round >= self.max_global_rounds or
                self.status == TaskStatus.COMPLETED)
    
    @property
    def is_expired(self) -> bool:
        """Check if task has expired (past deadline)"""
        return self.status == TaskStatus.EXPIRED
    
    @property
    def remaining_time(self, current_time: float) -> float:
        """Get remaining time until deadline"""
        return max(0, self.deadline - current_time)
    
    def update_accuracy(self, new_accuracy: float, current_time: float):
        """Update task accuracy after a global round"""
        self.current_accuracy = new_accuracy
        self.current_round += 1
        
        # Record round history
        round_info = {
            'round': self.current_round,
            'accuracy': new_accuracy,
            'time': current_time,
            'participants': self.assigned_clients.copy()
        }
        self.round_history.append(round_info)
        
        # Update status
        if self.is_completed:
            self.status = TaskStatus.COMPLETED
    
    def add_energy_consumption(self, energy: float):
        """Add energy consumption for this task"""
        self.total_energy_consumed += energy
    
    def get_progress_ratio(self) -> float:
        """Get task progress as ratio (0.0 to 1.0)"""
        if self.target_accuracy <= 0:
            return 0.0
        return min(1.0, self.current_accuracy / self.target_accuracy)
    
    def get_urgency_score(self, current_time: float) -> float:
        """
        Calculate urgency score based on remaining time and progress
        Higher score means more urgent
        """
        remaining_time = self.remaining_time(current_time)
        if remaining_time <= 0:
            return float('inf')  # Expired tasks are most urgent
        
        progress = self.get_progress_ratio()
        remaining_work = 1.0 - progress
        
        # Urgency increases as deadline approaches and work remains
        urgency = remaining_work / remaining_time
        return urgency
    
    def to_dict(self) -> Dict:
        """Convert task to dictionary representation"""
        return {
            'task_id': self.task_id,
            'arrival_time': self.arrival_time,
            'deadline': self.deadline,
            'target_accuracy': self.target_accuracy,
            'data_sizes': self.data_sizes,
            'model_size': self.model_size,
            'max_global_rounds': self.max_global_rounds,
            'current_round': self.current_round,
            'current_accuracy': self.current_accuracy,
            'status': self.status.value,
            'assigned_clients': self.assigned_clients,
            'total_energy_consumed': self.total_energy_consumed,
            'total_time_elapsed': self.total_time_elapsed,
            'progress_ratio': self.get_progress_ratio()
        }


def create_random_task(task_id: int, arrival_time: float, 
                      client_ids: List[int], config: Dict) -> FLTask:
    """
    Create a random FL task with given parameters
    
    Args:
        task_id: Unique task identifier
        arrival_time: Time when task arrives
        client_ids: List of available client IDs
        config: Configuration dictionary with task parameters
    
    Returns:
        FLTask: Randomly generated FL task
    """
    # Random deadline
    min_deadline = config.get('min_deadline', 100)
    max_deadline = config.get('max_deadline', 500)
    deadline = arrival_time + np.random.uniform(min_deadline, max_deadline)
    
    # Random target accuracy
    min_acc = config.get('min_target_accuracy', 0.8)
    max_acc = config.get('max_target_accuracy', 0.95)
    target_accuracy = np.random.uniform(min_acc, max_acc)
    
    # Random data sizes for each client
    min_data = config.get('min_data_size', 1000)
    max_data = config.get('max_data_size', 10000)
    data_sizes = {
        client_id: np.random.randint(min_data, max_data + 1)
        for client_id in client_ids
    }
    
    # Random model size (1-50 MB)
    model_size = np.random.uniform(1.0, 50.0)
    
    # Random max global rounds
    max_rounds = config.get('max_global_rounds', 20)
    max_global_rounds = np.random.randint(5, max_rounds + 1)
    
    return FLTask(
        task_id=task_id,
        arrival_time=arrival_time,
        deadline=deadline,
        target_accuracy=target_accuracy,
        data_sizes=data_sizes,
        model_size=model_size,
        max_global_rounds=max_global_rounds
    )
