"""
Task Manager for handling FL task lifecycle and events
"""
import numpy as np
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
import heapq

from .fl_task import FLTask, TaskStatus, create_random_task


class TaskEvent:
    """Represents a task-related event in the simulation"""
    
    def __init__(self, event_type: str, time: float, task_id: int, 
                 client_id: Optional[int] = None, **kwargs):
        self.event_type = event_type  # 'arrival', 'round_complete', 'deadline'
        self.time = time
        self.task_id = task_id
        self.client_id = client_id
        self.kwargs = kwargs
    
    def __lt__(self, other):
        return self.time < other.time
    
    def __repr__(self):
        return f"TaskEvent({self.event_type}, t={self.time:.2f}, task={self.task_id})"


class TaskManager:
    """
    Manages the lifecycle of FL tasks and generates events for the simulation
    """
    
    def __init__(self, config: Dict, client_ids: List[int]):
        self.config = config
        self.client_ids = client_ids
        self.tasks: Dict[int, FLTask] = {}
        self.event_queue = []  # Priority queue of TaskEvent objects
        self.next_task_id = 1
        self.current_time = 0.0
        
        # Task assignment tracking
        self.client_tasks: Dict[int, List[int]] = defaultdict(list)  # client_id -> task_ids
        
        # Statistics
        self.completed_tasks = []
        self.expired_tasks = []
        self.total_tasks_generated = 0
        
        # Task arrival parameters
        self.arrival_rate = config.get('arrival_rate', 0.1)
        
        # Schedule initial task arrivals
        self._schedule_initial_arrivals()
    
    def _schedule_initial_arrivals(self):
        """Schedule initial task arrivals using Poisson process"""
        max_time = self.config.get('max_simulation_time', 10000)
        current_time = 0.0
        
        while current_time < max_time:
            # Generate next arrival time using exponential distribution
            inter_arrival_time = np.random.exponential(1.0 / self.arrival_rate)
            current_time += inter_arrival_time
            
            if current_time >= max_time:
                break
            
            # Randomly assign to a client
            client_id = np.random.choice(self.client_ids)
            
            # Create arrival event
            event = TaskEvent('arrival', current_time, self.next_task_id, client_id)
            heapq.heappush(self.event_queue, event)
            self.next_task_id += 1
    
    def get_next_event(self) -> Optional[TaskEvent]:
        """Get the next event from the queue"""
        if not self.event_queue:
            return None
        return heapq.heappop(self.event_queue)
    
    def handle_task_arrival(self, event: TaskEvent) -> FLTask:
        """
        Handle a task arrival event
        
        Args:
            event: TaskEvent with type 'arrival'
            
        Returns:
            FLTask: The newly created task
        """
        # Create new task
        task = create_random_task(
            task_id=event.task_id,
            arrival_time=event.time,
            client_ids=self.client_ids,
            config=self.config['tasks']
        )
        
        # Store task
        self.tasks[task.task_id] = task
        self.total_tasks_generated += 1
        
        # Assign to client
        self.client_tasks[event.client_id].append(task.task_id)
        
        # Schedule deadline event
        deadline_event = TaskEvent('deadline', task.deadline, task.task_id)
        heapq.heappush(self.event_queue, deadline_event)
        
        return task
    
    def handle_round_completion(self, task_id: int, client_id: int, 
                              current_time: float, new_accuracy: float):
        """
        Handle completion of a global round for a task
        
        Args:
            task_id: ID of the completed task round
            client_id: ID of the client that completed the round
            current_time: Current simulation time
            new_accuracy: New accuracy achieved after this round
        """
        if task_id not in self.tasks:
            return
        
        task = self.tasks[task_id]
        task.update_accuracy(new_accuracy, current_time)
        
        # Create round completion event for environment
        event = TaskEvent('round_complete', current_time, task_id, client_id,
                         new_accuracy=new_accuracy)
        heapq.heappush(self.event_queue, event)
        
        # Check if task is completed
        if task.is_completed:
            self.completed_tasks.append(task_id)
            # Remove from client's active tasks
            if task_id in self.client_tasks[client_id]:
                self.client_tasks[client_id].remove(task_id)
    
    def handle_deadline_expiry(self, event: TaskEvent):
        """Handle task deadline expiry"""
        if event.task_id not in self.tasks:
            return
        
        task = self.tasks[event.task_id]
        if task.status not in [TaskStatus.COMPLETED, TaskStatus.EXPIRED]:
            task.status = TaskStatus.EXPIRED
            self.expired_tasks.append(event.task_id)
            
            # Remove from all clients' active tasks
            for client_id in self.client_ids:
                if event.task_id in self.client_tasks[client_id]:
                    self.client_tasks[client_id].remove(event.task_id)
    
    def get_client_tasks(self, client_id: int) -> List[FLTask]:
        """Get all active tasks for a specific client"""
        task_ids = self.client_tasks[client_id]
        return [self.tasks[tid] for tid in task_ids if tid in self.tasks]
    
    def get_task(self, task_id: int) -> Optional[FLTask]:
        """Get a specific task by ID"""
        return self.tasks.get(task_id)
    
    def get_active_tasks(self) -> List[FLTask]:
        """Get all active (non-completed, non-expired) tasks"""
        active_tasks = []
        for task in self.tasks.values():
            if task.status not in [TaskStatus.COMPLETED, TaskStatus.EXPIRED]:
                active_tasks.append(task)
        return active_tasks
    
    def update_time(self, current_time: float):
        """Update current simulation time"""
        self.current_time = current_time
    
    def get_statistics(self) -> Dict:
        """Get task management statistics"""
        active_tasks = self.get_active_tasks()
        
        return {
            'total_generated': self.total_tasks_generated,
            'active_tasks': len(active_tasks),
            'completed_tasks': len(self.completed_tasks),
            'expired_tasks': len(self.expired_tasks),
            'completion_rate': len(self.completed_tasks) / max(1, self.total_tasks_generated),
            'expiry_rate': len(self.expired_tasks) / max(1, self.total_tasks_generated),
            'pending_events': len(self.event_queue)
        }
    
    def cleanup_completed_tasks(self, keep_recent: int = 100):
        """Clean up old completed/expired tasks to save memory"""
        # Keep only recent completed tasks for statistics
        if len(self.completed_tasks) > keep_recent:
            old_completed = self.completed_tasks[:-keep_recent]
            for task_id in old_completed:
                if task_id in self.tasks:
                    del self.tasks[task_id]
            self.completed_tasks = self.completed_tasks[-keep_recent:]
        
        # Similar cleanup for expired tasks
        if len(self.expired_tasks) > keep_recent:
            old_expired = self.expired_tasks[:-keep_recent]
            for task_id in old_expired:
                if task_id in self.tasks:
                    del self.tasks[task_id]
            self.expired_tasks = self.expired_tasks[-keep_recent:]
