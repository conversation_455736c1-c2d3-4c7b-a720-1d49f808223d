"""
Configuration Loading Utilities
"""
import yaml
import os
from typing import Dict, Any


def load_config(config_path: str) -> Dict[str, Any]:
    """
    Load configuration from YAML file
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        Dict: Configuration dictionary
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r') as file:
        config = yaml.safe_load(file)
    
    # Validate required sections
    required_sections = ['environment', 'clients', 'tasks', 'maddpg', 'training', 'rewards']
    for section in required_sections:
        if section not in config:
            raise ValueError(f"Missing required configuration section: {section}")
    
    return config


def validate_config(config: Dict[str, Any]) -> bool:
    """
    Validate configuration parameters
    
    Args:
        config: Configuration dictionary
        
    Returns:
        bool: True if valid
    """
    try:
        # Validate numeric ranges
        assert config['clients']['num_clients'] > 0, "Number of clients must be positive"
        assert config['clients']['total_computation_capacity'] > 0, "Computation capacity must be positive"
        assert config['clients']['total_bandwidth_capacity'] > 0, "Bandwidth capacity must be positive"
        assert config['clients']['max_power_consumption'] > 0, "Max power must be positive"
        
        assert 0 < config['tasks']['arrival_rate'] <= 1.0, "Arrival rate must be in (0, 1]"
        assert config['tasks']['min_deadline'] > 0, "Min deadline must be positive"
        assert config['tasks']['max_deadline'] > config['tasks']['min_deadline'], "Max deadline must be > min deadline"
        
        assert 0 < config['maddpg']['actor_lr'] <= 1.0, "Actor learning rate must be in (0, 1]"
        assert 0 < config['maddpg']['critic_lr'] <= 1.0, "Critic learning rate must be in (0, 1]"
        assert 0 < config['maddpg']['gamma'] <= 1.0, "Gamma must be in (0, 1]"
        assert 0 < config['maddpg']['tau'] <= 1.0, "Tau must be in (0, 1]"
        
        assert config['training']['num_episodes'] > 0, "Number of episodes must be positive"
        assert config['training']['max_episode_length'] > 0, "Max episode length must be positive"
        
        return True
        
    except AssertionError as e:
        print(f"Configuration validation error: {e}")
        return False


def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two configuration dictionaries
    
    Args:
        base_config: Base configuration
        override_config: Override configuration
        
    Returns:
        Dict: Merged configuration
    """
    merged = base_config.copy()
    
    for key, value in override_config.items():
        if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
            merged[key] = merge_configs(merged[key], value)
        else:
            merged[key] = value
    
    return merged


def save_config(config: Dict[str, Any], filepath: str):
    """
    Save configuration to YAML file
    
    Args:
        config: Configuration dictionary
        filepath: Output file path
    """
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    
    with open(filepath, 'w') as file:
        yaml.dump(config, file, default_flow_style=False, indent=2)
