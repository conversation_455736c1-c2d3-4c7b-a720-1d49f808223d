"""
Logging Utilities for MADDPG FL System
"""
import os
import json
import time
from typing import Dict, Any, List
from collections import defaultdict
import numpy as np


class Logger:
    """
    Logger for training metrics and system performance
    """
    
    def __init__(self, log_dir: str, experiment_name: str = None):
        self.log_dir = log_dir
        self.experiment_name = experiment_name or f"experiment_{int(time.time())}"
        
        # Create log directory
        self.experiment_dir = os.path.join(log_dir, self.experiment_name)
        os.makedirs(self.experiment_dir, exist_ok=True)
        
        # Initialize log storage
        self.episode_logs = []
        self.training_logs = defaultdict(list)
        self.system_metrics = defaultdict(list)
        
        # Performance tracking
        self.start_time = time.time()
        self.episode_start_time = None
        
        print(f"Logger initialized. Logs will be saved to: {self.experiment_dir}")
    
    def log_episode_start(self, episode: int):
        """Log episode start"""
        self.episode_start_time = time.time()
        print(f"\n=== Episode {episode} Started ===")
    
    def log_episode_end(self, episode: int, episode_stats: Dict[str, Any]):
        """Log episode completion"""
        episode_duration = time.time() - self.episode_start_time if self.episode_start_time else 0
        
        # Add timing information
        episode_stats['episode'] = episode
        episode_stats['duration'] = episode_duration
        episode_stats['timestamp'] = time.time()
        
        # Store episode log
        self.episode_logs.append(episode_stats)
        
        # Print summary
        print(f"Episode {episode} completed in {episode_duration:.2f}s")
        print(f"  Tasks completed: {episode_stats.get('total_tasks_completed', 0)}")
        print(f"  Tasks expired: {episode_stats.get('total_tasks_expired', 0)}")
        print(f"  Total energy: {episode_stats.get('total_energy_consumed', 0):.2f}")
        
        # Print agent rewards
        avg_rewards = episode_stats.get('avg_rewards', {})
        if avg_rewards:
            print("  Agent rewards:", {f"Agent{k}": f"{v:.3f}" for k, v in avg_rewards.items()})
    
    def log_training_step(self, episode: int, step: int, training_results: Dict[int, Dict[str, float]]):
        """Log training step results"""
        for agent_id, losses in training_results.items():
            for loss_type, loss_value in losses.items():
                self.training_logs[f'agent_{agent_id}_{loss_type}'].append({
                    'episode': episode,
                    'step': step,
                    'value': loss_value,
                    'timestamp': time.time()
                })
    
    def log_system_metrics(self, episode: int, metrics: Dict[str, Any]):
        """Log system-wide metrics"""
        for metric_name, metric_value in metrics.items():
            self.system_metrics[metric_name].append({
                'episode': episode,
                'value': metric_value,
                'timestamp': time.time()
            })
    
    def save_logs(self):
        """Save all logs to files"""
        # Save episode logs
        episode_log_path = os.path.join(self.experiment_dir, 'episode_logs.json')
        with open(episode_log_path, 'w') as f:
            json.dump(self.episode_logs, f, indent=2)
        
        # Save training logs
        training_log_path = os.path.join(self.experiment_dir, 'training_logs.json')
        with open(training_log_path, 'w') as f:
            json.dump(dict(self.training_logs), f, indent=2)
        
        # Save system metrics
        metrics_log_path = os.path.join(self.experiment_dir, 'system_metrics.json')
        with open(metrics_log_path, 'w') as f:
            json.dump(dict(self.system_metrics), f, indent=2)
        
        print(f"Logs saved to {self.experiment_dir}")
    
    def get_episode_summary(self, last_n_episodes: int = 10) -> Dict[str, Any]:
        """Get summary of recent episodes"""
        if not self.episode_logs:
            return {}
        
        recent_episodes = self.episode_logs[-last_n_episodes:]
        
        # Calculate averages
        avg_completion_rate = np.mean([
            ep.get('total_tasks_completed', 0) / max(1, 
                ep.get('total_tasks_completed', 0) + ep.get('total_tasks_expired', 0))
            for ep in recent_episodes
        ])
        
        avg_energy = np.mean([ep.get('total_energy_consumed', 0) for ep in recent_episodes])
        avg_duration = np.mean([ep.get('duration', 0) for ep in recent_episodes])
        
        # Agent performance
        agent_rewards = defaultdict(list)
        for ep in recent_episodes:
            for agent_id, reward in ep.get('avg_rewards', {}).items():
                agent_rewards[agent_id].append(reward)
        
        avg_agent_rewards = {
            agent_id: np.mean(rewards) for agent_id, rewards in agent_rewards.items()
        }
        
        return {
            'episodes_analyzed': len(recent_episodes),
            'avg_completion_rate': avg_completion_rate,
            'avg_energy_consumption': avg_energy,
            'avg_episode_duration': avg_duration,
            'avg_agent_rewards': avg_agent_rewards
        }
    
    def print_training_progress(self, episode: int, total_episodes: int):
        """Print training progress"""
        progress = episode / total_episodes
        elapsed_time = time.time() - self.start_time
        estimated_total = elapsed_time / progress if progress > 0 else 0
        remaining_time = estimated_total - elapsed_time
        
        print(f"\nTraining Progress: {episode}/{total_episodes} ({progress*100:.1f}%)")
        print(f"Elapsed: {elapsed_time/3600:.2f}h, Remaining: {remaining_time/3600:.2f}h")
        
        # Recent performance summary
        if episode > 0 and episode % 10 == 0:
            summary = self.get_episode_summary(10)
            if summary:
                print(f"Recent 10 episodes:")
                print(f"  Completion rate: {summary['avg_completion_rate']:.3f}")
                print(f"  Avg energy: {summary['avg_energy_consumption']:.2f}")
                print(f"  Avg duration: {summary['avg_episode_duration']:.2f}s")
    
    def export_csv(self, filename: str = None):
        """Export episode logs to CSV"""
        try:
            import pandas as pd
            
            if not self.episode_logs:
                print("No episode logs to export")
                return
            
            # Convert to DataFrame
            df = pd.DataFrame(self.episode_logs)
            
            # Save to CSV
            csv_path = os.path.join(self.experiment_dir, filename or 'episode_data.csv')
            df.to_csv(csv_path, index=False)
            
            print(f"Episode data exported to {csv_path}")
            
        except ImportError:
            print("pandas not available for CSV export")
    
    def get_best_episode(self, metric: str = 'avg_completion_rate') -> Dict[str, Any]:
        """Get the best performing episode based on a metric"""
        if not self.episode_logs:
            return {}
        
        # Calculate metric for each episode
        episode_metrics = []
        for ep in self.episode_logs:
            if metric == 'avg_completion_rate':
                total_tasks = ep.get('total_tasks_completed', 0) + ep.get('total_tasks_expired', 0)
                value = ep.get('total_tasks_completed', 0) / max(1, total_tasks)
            elif metric == 'total_reward':
                value = sum(ep.get('total_rewards', {}).values())
            elif metric == 'energy_efficiency':
                energy = ep.get('total_energy_consumed', 1)
                completed = ep.get('total_tasks_completed', 0)
                value = completed / energy if energy > 0 else 0
            else:
                value = ep.get(metric, 0)
            
            episode_metrics.append((value, ep))
        
        # Find best episode
        best_value, best_episode = max(episode_metrics, key=lambda x: x[0])
        
        return {
            'episode': best_episode.get('episode', 0),
            'metric_value': best_value,
            'episode_data': best_episode
        }
