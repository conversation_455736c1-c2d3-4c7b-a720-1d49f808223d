"""
Visualization utilities for MADDPG FL system
"""
import matplotlib.pyplot as plt
import numpy as np
from typing import Dict, List, Any, Optional
import seaborn as sns


def plot_training_curves(episode_logs: List[Dict], save_path: str = None):
    """
    Plot training curves from episode logs
    
    Args:
        episode_logs: List of episode log dictionaries
        save_path: Path to save the plot
    """
    if not episode_logs:
        print("No episode logs to plot")
        return
    
    # Extract data
    episodes = [log['episode'] for log in episode_logs]
    completion_rates = []
    energy_consumption = []
    avg_rewards = []
    
    for log in episode_logs:
        # Completion rate
        total_completed = log.get('total_tasks_completed', 0)
        total_expired = log.get('total_tasks_expired', 0)
        total_tasks = total_completed + total_expired
        completion_rate = total_completed / max(1, total_tasks)
        completion_rates.append(completion_rate)
        
        # Energy consumption
        energy_consumption.append(log.get('total_energy_consumed', 0))
        
        # Average reward across agents
        agent_rewards = log.get('avg_rewards', {})
        avg_reward = np.mean(list(agent_rewards.values())) if agent_rewards else 0
        avg_rewards.append(avg_reward)
    
    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('MADDPG Training Progress', fontsize=16)
    
    # Completion rate
    axes[0, 0].plot(episodes, completion_rates, 'b-', alpha=0.7)
    axes[0, 0].set_title('Task Completion Rate')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Completion Rate')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Energy consumption
    axes[0, 1].plot(episodes, energy_consumption, 'r-', alpha=0.7)
    axes[0, 1].set_title('Energy Consumption')
    axes[0, 1].set_xlabel('Episode')
    axes[0, 1].set_ylabel('Total Energy')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Average reward
    axes[1, 0].plot(episodes, avg_rewards, 'g-', alpha=0.7)
    axes[1, 0].set_title('Average Agent Reward')
    axes[1, 0].set_xlabel('Episode')
    axes[1, 0].set_ylabel('Reward')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Moving averages
    window = min(50, len(episodes) // 10)
    if window > 1:
        moving_completion = np.convolve(completion_rates, np.ones(window)/window, mode='valid')
        moving_episodes = episodes[window-1:]
        axes[1, 1].plot(moving_episodes, moving_completion, 'b-', label=f'Completion Rate (MA-{window})')
        
        moving_rewards = np.convolve(avg_rewards, np.ones(window)/window, mode='valid')
        axes[1, 1].plot(moving_episodes, moving_rewards, 'g-', label=f'Avg Reward (MA-{window})')
        
        axes[1, 1].set_title('Moving Averages')
        axes[1, 1].set_xlabel('Episode')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training curves saved to {save_path}")
    
    plt.show()


def plot_system_metrics(episode_logs: List[Dict], save_path: str = None):
    """
    Plot system-wide performance metrics
    
    Args:
        episode_logs: List of episode log dictionaries
        save_path: Path to save the plot
    """
    if not episode_logs:
        print("No episode logs to plot")
        return
    
    # Extract system metrics
    episodes = [log['episode'] for log in episode_logs]
    task_stats = []
    client_stats = []
    
    for log in episode_logs:
        task_stats.append(log.get('task_stats', {}))
        client_stats.append(log.get('client_stats', {}))
    
    # Create figure
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    fig.suptitle('System Performance Metrics', fontsize=16)
    
    # Task completion trends
    completion_rates = [stats.get('completion_rate', 0) for stats in task_stats]
    expiry_rates = [stats.get('expiry_rate', 0) for stats in task_stats]
    
    axes[0, 0].plot(episodes, completion_rates, 'g-', label='Completion Rate')
    axes[0, 0].plot(episodes, expiry_rates, 'r-', label='Expiry Rate')
    axes[0, 0].set_title('Task Success Rates')
    axes[0, 0].set_xlabel('Episode')
    axes[0, 0].set_ylabel('Rate')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Active tasks over time
    active_tasks = [stats.get('active_tasks', 0) for stats in task_stats]
    axes[0, 1].plot(episodes, active_tasks, 'b-')
    axes[0, 1].set_title('Active Tasks')
    axes[0, 1].set_xlabel('Episode')
    axes[0, 1].set_ylabel('Number of Tasks')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Resource utilization (average across clients)
    avg_cpu_util = []
    avg_bandwidth_util = []
    avg_power_util = []
    
    for client_stat in client_stats:
        if client_stat:
            cpu_utils = [client.get('resource_utilization', {}).get('avg_computation_utilization', 0) 
                        for client in client_stat.values()]
            bw_utils = [client.get('resource_utilization', {}).get('avg_bandwidth_utilization', 0) 
                       for client in client_stat.values()]
            power_utils = [client.get('resource_utilization', {}).get('avg_power_utilization', 0) 
                          for client in client_stat.values()]
            
            avg_cpu_util.append(np.mean(cpu_utils) if cpu_utils else 0)
            avg_bandwidth_util.append(np.mean(bw_utils) if bw_utils else 0)
            avg_power_util.append(np.mean(power_utils) if power_utils else 0)
        else:
            avg_cpu_util.append(0)
            avg_bandwidth_util.append(0)
            avg_power_util.append(0)
    
    axes[0, 2].plot(episodes, avg_cpu_util, 'r-', label='CPU')
    axes[0, 2].plot(episodes, avg_bandwidth_util, 'g-', label='Bandwidth')
    axes[0, 2].plot(episodes, avg_power_util, 'b-', label='Power')
    axes[0, 2].set_title('Average Resource Utilization')
    axes[0, 2].set_xlabel('Episode')
    axes[0, 2].set_ylabel('Utilization')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    
    # Energy efficiency (tasks completed per unit energy)
    energy_efficiency = []
    for log in episode_logs:
        completed = log.get('total_tasks_completed', 0)
        energy = log.get('total_energy_consumed', 1)
        efficiency = completed / energy if energy > 0 else 0
        energy_efficiency.append(efficiency)
    
    axes[1, 0].plot(episodes, energy_efficiency, 'purple')
    axes[1, 0].set_title('Energy Efficiency')
    axes[1, 0].set_xlabel('Episode')
    axes[1, 0].set_ylabel('Tasks/Energy')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Episode duration
    durations = [log.get('duration', 0) for log in episode_logs]
    axes[1, 1].plot(episodes, durations, 'orange')
    axes[1, 1].set_title('Episode Duration')
    axes[1, 1].set_xlabel('Episode')
    axes[1, 1].set_ylabel('Duration (s)')
    axes[1, 1].grid(True, alpha=0.3)
    
    # Agent reward distribution (latest episode)
    if episode_logs:
        latest_rewards = episode_logs[-1].get('total_rewards', {})
        if latest_rewards:
            agent_ids = list(latest_rewards.keys())
            rewards = list(latest_rewards.values())
            
            axes[1, 2].bar(range(len(agent_ids)), rewards)
            axes[1, 2].set_title('Agent Rewards (Latest Episode)')
            axes[1, 2].set_xlabel('Agent ID')
            axes[1, 2].set_ylabel('Total Reward')
            axes[1, 2].set_xticks(range(len(agent_ids)))
            axes[1, 2].set_xticklabels([f'A{aid}' for aid in agent_ids])
            axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"System metrics plot saved to {save_path}")
    
    plt.show()


def plot_agent_comparison(episode_logs: List[Dict], save_path: str = None):
    """
    Plot comparison between different agents
    
    Args:
        episode_logs: List of episode log dictionaries
        save_path: Path to save the plot
    """
    if not episode_logs:
        print("No episode logs to plot")
        return
    
    # Extract agent performance data
    agent_rewards = {}
    agent_energy = {}
    
    for log in episode_logs:
        episode = log['episode']
        
        # Agent rewards
        for agent_id, reward in log.get('total_rewards', {}).items():
            if agent_id not in agent_rewards:
                agent_rewards[agent_id] = []
            agent_rewards[agent_id].append((episode, reward))
        
        # Agent energy consumption
        client_stats = log.get('client_stats', {})
        for client_id, stats in client_stats.items():
            energy = stats.get('total_energy_consumed', 0)
            if client_id not in agent_energy:
                agent_energy[client_id] = []
            agent_energy[client_id].append((episode, energy))
    
    # Create comparison plots
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    fig.suptitle('Agent Performance Comparison', fontsize=16)
    
    # Reward comparison
    for agent_id, rewards in agent_rewards.items():
        episodes, reward_values = zip(*rewards)
        axes[0].plot(episodes, reward_values, label=f'Agent {agent_id}', alpha=0.7)
    
    axes[0].set_title('Agent Rewards Over Time')
    axes[0].set_xlabel('Episode')
    axes[0].set_ylabel('Total Reward')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Energy comparison
    for agent_id, energy in agent_energy.items():
        episodes, energy_values = zip(*energy)
        axes[1].plot(episodes, energy_values, label=f'Agent {agent_id}', alpha=0.7)
    
    axes[1].set_title('Agent Energy Consumption Over Time')
    axes[1].set_xlabel('Episode')
    axes[1].set_ylabel('Total Energy')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Agent comparison plot saved to {save_path}")
    
    plt.show()


def create_performance_dashboard(episode_logs: List[Dict], save_dir: str = None):
    """
    Create a comprehensive performance dashboard
    
    Args:
        episode_logs: List of episode log dictionaries
        save_dir: Directory to save plots
    """
    if save_dir:
        import os
        os.makedirs(save_dir, exist_ok=True)
        
        # Generate all plots
        plot_training_curves(episode_logs, os.path.join(save_dir, 'training_curves.png'))
        plot_system_metrics(episode_logs, os.path.join(save_dir, 'system_metrics.png'))
        plot_agent_comparison(episode_logs, os.path.join(save_dir, 'agent_comparison.png'))
        
        print(f"Performance dashboard saved to {save_dir}")
    else:
        # Display plots
        plot_training_curves(episode_logs)
        plot_system_metrics(episode_logs)
        plot_agent_comparison(episode_logs)
