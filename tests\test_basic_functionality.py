"""
Basic functionality tests for MADDPG FL system
"""
import unittest
import numpy as np
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.tasks.fl_task import FLTask, create_random_task
from src.clients.client import Client
from src.clients.resource_manager import ResourceManager, ScheduleEntry
from src.server.fl_server import FLServer
from src.environment.maddpg_env import MADDPGEnvironment
from src.utils.config_loader import load_config


class TestFLTask(unittest.TestCase):
    """Test FL Task functionality"""
    
    def setUp(self):
        self.task = FLTask(
            task_id=1,
            arrival_time=0.0,
            deadline=100.0,
            target_accuracy=0.9,
            data_sizes={0: 1000, 1: 1500},
            model_size=10.0,
            max_global_rounds=10
        )
    
    def test_task_creation(self):
        """Test task creation"""
        self.assertEqual(self.task.task_id, 1)
        self.assertEqual(self.task.target_accuracy, 0.9)
        self.assertFalse(self.task.is_completed)
        self.assertFalse(self.task.is_expired)
    
    def test_accuracy_update(self):
        """Test accuracy update"""
        self.task.update_accuracy(0.5, 10.0)
        self.assertEqual(self.task.current_accuracy, 0.5)
        self.assertEqual(self.task.current_round, 1)
        self.assertFalse(self.task.is_completed)
        
        # Complete task
        self.task.update_accuracy(0.9, 20.0)
        self.assertTrue(self.task.is_completed)
    
    def test_urgency_score(self):
        """Test urgency score calculation"""
        urgency_early = self.task.get_urgency_score(10.0)
        urgency_late = self.task.get_urgency_score(90.0)
        
        # Urgency should increase as deadline approaches
        self.assertGreater(urgency_late, urgency_early)
    
    def test_random_task_creation(self):
        """Test random task creation"""
        config = {
            'min_deadline': 50,
            'max_deadline': 200,
            'min_target_accuracy': 0.8,
            'max_target_accuracy': 0.95,
            'min_data_size': 500,
            'max_data_size': 2000,
            'max_global_rounds': 15
        }
        
        task = create_random_task(2, 5.0, [0, 1, 2], config)
        
        self.assertEqual(task.task_id, 2)
        self.assertEqual(task.arrival_time, 5.0)
        self.assertGreaterEqual(task.deadline, 55.0)  # arrival_time + min_deadline
        self.assertGreaterEqual(task.target_accuracy, 0.8)
        self.assertLessEqual(task.target_accuracy, 0.95)


class TestResourceManager(unittest.TestCase):
    """Test Resource Manager functionality"""
    
    def setUp(self):
        self.resource_manager = ResourceManager(
            client_id=0,
            total_computation=100.0,
            total_bandwidth=50.0,
            max_power=10.0
        )
    
    def test_schedule_entry_creation(self):
        """Test schedule entry creation"""
        entry = ScheduleEntry(
            start_time=0.0,
            end_time=10.0,
            task_id=1,
            round_num=1,
            computation_ratio=0.5,
            bandwidth_allocation=25.0,
            power_consumption=5.0
        )
        
        self.assertEqual(entry.duration, 10.0)
        self.assertFalse(entry.overlaps_with(ScheduleEntry(
            start_time=15.0, end_time=20.0, task_id=2, round_num=1,
            computation_ratio=0.3, bandwidth_allocation=15.0, power_consumption=3.0
        )))
    
    def test_add_schedule_entry(self):
        """Test adding schedule entries"""
        entry1 = ScheduleEntry(
            start_time=0.0, end_time=10.0, task_id=1, round_num=1,
            computation_ratio=0.5, bandwidth_allocation=25.0, power_consumption=5.0
        )
        
        success = self.resource_manager.add_schedule_entry(entry1)
        self.assertTrue(success)
        self.assertEqual(len(self.resource_manager.schedule_queue), 1)
        
        # Try to add overlapping entry
        entry2 = ScheduleEntry(
            start_time=5.0, end_time=15.0, task_id=2, round_num=1,
            computation_ratio=0.6, bandwidth_allocation=30.0, power_consumption=6.0
        )
        
        success = self.resource_manager.add_schedule_entry(entry2)
        self.assertFalse(success)  # Should fail due to overlap
    
    def test_find_available_slot(self):
        """Test finding available time slots"""
        # Add an entry
        entry = ScheduleEntry(
            start_time=10.0, end_time=20.0, task_id=1, round_num=1,
            computation_ratio=0.5, bandwidth_allocation=25.0, power_consumption=5.0
        )
        self.resource_manager.add_schedule_entry(entry)
        
        # Find slot before existing entry
        slot = self.resource_manager.find_next_available_slot(
            duration=5.0, computation_ratio=0.3, bandwidth_needed=15.0, power_needed=3.0
        )
        self.assertIsNotNone(slot)
        self.assertLessEqual(slot + 5.0, 10.0)
        
        # Find slot after existing entry
        slot = self.resource_manager.find_next_available_slot(
            duration=5.0, computation_ratio=0.3, bandwidth_needed=15.0, 
            power_needed=3.0, earliest_start=25.0
        )
        self.assertIsNotNone(slot)
        self.assertGreaterEqual(slot, 25.0)


class TestClient(unittest.TestCase):
    """Test Client functionality"""
    
    def setUp(self):
        self.config = {
            'clients': {
                'total_computation_capacity': 100.0,
                'total_bandwidth_capacity': 50.0,
                'max_power_consumption': 10.0
            }
        }
        self.client = Client(0, self.config)
        
        self.task = FLTask(
            task_id=1, arrival_time=0.0, deadline=100.0, target_accuracy=0.9,
            data_sizes={0: 1000}, model_size=10.0, max_global_rounds=10
        )
    
    def test_client_creation(self):
        """Test client creation"""
        self.assertEqual(self.client.client_id, 0)
        self.assertEqual(self.client.total_computation, 100.0)
        self.assertEqual(len(self.client.task_queue), 0)
    
    def test_add_remove_task(self):
        """Test adding and removing tasks"""
        self.client.add_task(self.task)
        self.assertEqual(len(self.client.task_queue), 1)
        self.assertIn(0, self.task.assigned_clients)
        
        self.client.remove_task(1)
        self.assertEqual(len(self.client.task_queue), 0)
        self.assertIn(1, self.client.completed_tasks)
    
    def test_computation_time_calculation(self):
        """Test computation time calculation"""
        comp_time = self.client.calculate_computation_time(self.task, 0.5)
        self.assertGreater(comp_time, 0)
        
        # Higher computation ratio should reduce time
        comp_time_high = self.client.calculate_computation_time(self.task, 1.0)
        self.assertLess(comp_time_high, comp_time)
    
    def test_communication_time_calculation(self):
        """Test communication time calculation"""
        comm_time = self.client.calculate_communication_time(self.task, 25.0)
        self.assertGreater(comm_time, 0)
        
        # Higher bandwidth should reduce time
        comm_time_high = self.client.calculate_communication_time(self.task, 50.0)
        self.assertLess(comm_time_high, comm_time)
    
    def test_task_scheduling(self):
        """Test task scheduling"""
        self.client.add_task(self.task)
        
        action = {
            'selected': True,
            'computation_ratio': 0.5,
            'bandwidth': 25.0,
            'power': 5.0,
            'start_time': 0.0
        }
        
        success, entry = self.client.schedule_task_round(1, action)
        self.assertTrue(success)
        self.assertIsNotNone(entry)
        self.assertEqual(entry.task_id, 1)


class TestFLServer(unittest.TestCase):
    """Test FL Server functionality"""
    
    def setUp(self):
        self.config = {
            'min_participants': 1,
            'aggregation_method': 'fedavg'
        }
        self.server = FLServer(self.config)
        
        self.task = FLTask(
            task_id=1, arrival_time=0.0, deadline=100.0, target_accuracy=0.9,
            data_sizes={0: 1000, 1: 1500}, model_size=10.0, max_global_rounds=10
        )
    
    def test_task_registration(self):
        """Test task registration"""
        self.server.register_task(self.task)
        self.assertIn(1, self.server.global_models)
        self.assertEqual(len(self.server.round_accuracies[1]), 1)  # Initial accuracy
    
    def test_model_update_and_aggregation(self):
        """Test model update and aggregation"""
        self.server.register_task(self.task)
        
        # Receive model update
        success = self.server.receive_model_update(1, 0, 1, {}, 0.7, 1000)
        self.assertTrue(success)
        
        # Aggregate round
        success, new_accuracy = self.server.aggregate_round(1, 1, 10.0)
        self.assertTrue(success)
        self.assertGreater(new_accuracy, 0.0)
        self.assertEqual(len(self.server.round_accuracies[1]), 2)  # Initial + new


class TestEnvironment(unittest.TestCase):
    """Test Environment functionality"""
    
    def setUp(self):
        # Create minimal config for testing
        self.config = {
            'environment': {'max_simulation_time': 1000, 'time_step': 1.0},
            'clients': {
                'num_clients': 2,
                'total_computation_capacity': 100.0,
                'total_bandwidth_capacity': 50.0,
                'max_power_consumption': 10.0
            },
            'tasks': {
                'arrival_rate': 0.1,
                'min_deadline': 50,
                'max_deadline': 200,
                'min_target_accuracy': 0.8,
                'max_target_accuracy': 0.95,
                'min_data_size': 500,
                'max_data_size': 2000,
                'max_global_rounds': 10
            },
            'maddpg': {
                'actor_lr': 0.001,
                'critic_lr': 0.001,
                'gamma': 0.95,
                'tau': 0.01,
                'buffer_size': 10000,
                'batch_size': 32,
                'hidden_dim': 64
            },
            'training': {
                'num_episodes': 10,
                'max_episode_length': 100,
                'save_interval': 5,
                'log_interval': 2
            },
            'rewards': {
                'accuracy_weight': 1.0,
                'energy_weight': 0.5,
                'deadline_weight': 2.0,
                'completion_bonus': 10.0
            },
            'max_tasks_per_client': 5,
            'max_schedule_entries': 10
        }
    
    def test_environment_creation(self):
        """Test environment creation"""
        env = MADDPGEnvironment(self.config)
        self.assertEqual(len(env.clients), 2)
        self.assertEqual(len(env.agents), 2)
        self.assertIsNotNone(env.task_manager)
        self.assertIsNotNone(env.server)
    
    def test_environment_reset(self):
        """Test environment reset"""
        env = MADDPGEnvironment(self.config)
        states = env.reset()
        
        self.assertEqual(len(states), 2)  # Two clients
        for client_id, state in states.items():
            self.assertIsInstance(state, np.ndarray)
            self.assertGreater(len(state), 0)
    
    def test_environment_step(self):
        """Test environment step"""
        env = MADDPGEnvironment(self.config)
        states = env.reset()
        
        # Take a few steps
        for _ in range(5):
            next_states, rewards, dones, info = env.step()
            
            if all(dones.values()):
                break
            
            self.assertEqual(len(next_states), 2)
            self.assertEqual(len(rewards), 2)
            self.assertEqual(len(dones), 2)
            self.assertIsInstance(info, dict)
            
            states = next_states


if __name__ == '__main__':
    unittest.main()
